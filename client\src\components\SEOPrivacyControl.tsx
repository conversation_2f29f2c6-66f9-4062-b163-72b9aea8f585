import React, { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getGeneralSettings } from '@/api/generalSettings';

interface SEOPrivacyControlProps {
  pagePath?: string;
}

const SEOPrivacyControl: React.FC<SEOPrivacyControlProps> = ({ pagePath = '/' }) => {
  const { data: settings } = useQuery({
    queryKey: ['generalSettings'],
    queryFn: getGeneralSettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  useEffect(() => {
    if (!settings?.seoPrivacy) return;

    const { seoPrivacy } = settings;

    // Determine if this page should be indexed
    const shouldIndex = getShouldPageBeIndexed(pagePath, seoPrivacy);

    // Update robots meta tag
    updateRobotsMetaTag(shouldIndex, seoPrivacy);

    // Update referrer policy
    updateReferrerPolicy(seoPrivacy.privacyHeaders.disableReferrer);

    // Hide generator information
    if (seoPrivacy.hideFramework || seoPrivacy.privacyHeaders.hideGenerator) {
      hideGeneratorInfo();
    }

  }, [settings, pagePath]);

  return null; // This component doesn't render anything
};

function getShouldPageBeIndexed(path: string, seoPrivacy: any): boolean {
  // If global no-index is enabled, nothing should be indexed
  if (seoPrivacy.globalNoIndex || seoPrivacy.hideFromSearchEngines) {
    return false;
  }

  // Check specific page rules
  if (path === '/' || path === '/home') {
    return seoPrivacy.pageIndexingRules.homepage;
  }

  if (path.startsWith('/checkout/')) {
    return seoPrivacy.pageIndexingRules.checkoutPages;
  }

  if (path.startsWith('/admin/')) {
    return seoPrivacy.pageIndexingRules.adminPages;
  }

  // Default to custom pages rule
  return seoPrivacy.pageIndexingRules.customPages;
}

function updateRobotsMetaTag(shouldIndex: boolean, seoPrivacy: any) {
  // Remove existing robots meta tags
  const existingRobotsTags = document.querySelectorAll('meta[name="robots"], meta[name="googlebot"], meta[name="bingbot"], meta[name="slurp"], meta[name="duckduckbot"]');
  existingRobotsTags.forEach(tag => tag.remove());

  // Set robots content based on indexing rules
  const robotsContent = shouldIndex
    ? 'index, follow'
    : 'noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate';

  // Add robots meta tag
  const robotsTag = document.createElement('meta');
  robotsTag.name = 'robots';
  robotsTag.content = robotsContent;
  document.head.appendChild(robotsTag);

  // Add specific bot meta tags if hiding from search engines
  if (!shouldIndex || seoPrivacy.hideFromSearchEngines) {
    const bots = ['googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider', 'yandexbot'];
    bots.forEach(bot => {
      const botTag = document.createElement('meta');
      botTag.name = bot;
      botTag.content = 'noindex, nofollow, noarchive, nosnippet, noimageindex, notranslate';
      document.head.appendChild(botTag);
    });
  }
}

function updateReferrerPolicy(disableReferrer: boolean) {
  // Remove existing referrer meta tag
  const existingReferrerTag = document.querySelector('meta[name="referrer"]');
  if (existingReferrerTag) {
    existingReferrerTag.remove();
  }

  if (disableReferrer) {
    const referrerTag = document.createElement('meta');
    referrerTag.name = 'referrer';
    referrerTag.content = 'no-referrer';
    document.head.appendChild(referrerTag);
  }
}

function hideGeneratorInfo() {
  // Remove or empty generator meta tag
  const generatorTag = document.querySelector('meta[name="generator"]');
  if (generatorTag) {
    (generatorTag as HTMLMetaElement).content = '';
  } else {
    const newGeneratorTag = document.createElement('meta');
    newGeneratorTag.name = 'generator';
    newGeneratorTag.content = '';
    document.head.appendChild(newGeneratorTag);
  }

  // Remove any framework-specific meta tags
  const frameworkTags = document.querySelectorAll('meta[name*="react"], meta[name*="vite"], meta[name*="webpack"], meta[name*="next"], meta[name*="nuxt"]');
  frameworkTags.forEach(tag => tag.remove());
}

export default SEOPrivacyControl;
