import express, { Request, Response } from 'express';
import { isAdmin } from '../middleware/auth';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { storage } from '../storage';

const dataExportImportRouter = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      const uploadDir = path.join(__dirname, '../uploads');

      // Create uploads directory if it doesn't exist
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }

      cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      cb(null, 'import-' + uniqueSuffix + path.extname(file.originalname));
    }
  }),
  fileFilter: (req, file, cb) => {
    // Only accept JSON files
    if (file.mimetype === 'application/json' || path.extname(file.originalname).toLowerCase() === '.json') {
      cb(null, true);
    } else {
      cb(new Error('Only JSON files are allowed'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB max file size
  }
});

// Export products
dataExportImportRouter.get('/products', isAdmin, async (req: Request, res: Response) => {
  try {
    // Get all products
    const products = await storage.getProducts();

    // Set response headers for file download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="products-export-${Date.now()}.json"`);

    // Send the products as JSON
    res.json(products);
  } catch (error) {
    console.error('Error exporting products:', error);
    res.status(500).json({ message: 'Failed to export products' });
  }
});

// Export orders and invoices
dataExportImportRouter.get('/orders', isAdmin, async (req: Request, res: Response) => {
  try {
    // Get all invoices
    const invoices = await storage.getInvoices();

    // Set response headers for file download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="orders-export-${Date.now()}.json"`);

    // Send the invoices as JSON
    res.json(invoices);
  } catch (error) {
    console.error('Error exporting orders:', error);
    res.status(500).json({ message: 'Failed to export orders' });
  }
});

// Export settings
dataExportImportRouter.get('/settings', isAdmin, async (req: Request, res: Response) => {
  try {
    // Create hardcoded settings
    const settings = {
      general: {
        siteName: "PayPal Invoicer",
        siteDescription: "Generate and manage PayPal invoices",
        logoUrl: "",
        faviconUrl: "",
        primaryColor: "#0070ba",
        secondaryColor: "#003087",
        footerText: "© 2023 PayPal Invoicer",
        enableCheckout: true,
        enableCustomCheckout: true,
        enableTestMode: true,
        defaultTestCustomer: {
          enabled: true,
          name: "Test Customer",
          email: "<EMAIL>"
        },
        emailDomainRestriction: {
          enabled: true,
          allowedDomains: "gmail.com, hotmail.com, yahoo.com"
        }
      },
      email: {
        providers: [
          {
            id: 'smtp-1',
            name: 'Primary SMTP',
            active: true,
            isDefault: true,
            isBackup: false,
            credentials: {
              host: 'smtp-relay.example.com',
              port: '587',
              secure: false,
              auth: {
                user: '<EMAIL>',
                pass: '********'
              },
              fromEmail: '<EMAIL>',
              fromName: 'PayPal Invoicer'
            }
          }
        ]
      },
      payment: {
        providers: [
          {
            id: 'paypal',
            name: 'PayPal',
            active: true,
            config: {
              clientId: '********',
              clientSecret: '********',
              mode: 'sandbox',
              webhookId: '',
              paypalEmail: '<EMAIL>'
            }
          },
          {
            id: 'custom-link',
            name: 'Custom Payment Links',
            active: true,
            config: {
              links: [
                {
                  id: 'link-1',
                  name: 'Default Payment Link',
                  paymentLink: 'https://example.com/pay',
                  buttonText: 'Pay Now',
                  successRedirectUrl: 'https://example.com/thank-you',
                  active: true
                }
              ],
              rotationMethod: 'round-robin',
              lastUsedIndex: 0
            }
          }
        ]
      }
    };

    // Set response headers for file download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="settings-export-${Date.now()}.json"`);

    // Send the settings as JSON
    res.json(settings);
  } catch (error) {
    console.error('Error exporting settings:', error);
    res.status(500).json({ message: 'Failed to export settings' });
  }
});

// Export all data
dataExportImportRouter.get('/all', isAdmin, async (req: Request, res: Response) => {
  try {
    // Get data from storage
    const products = await storage.getProducts();
    const invoices = await storage.getInvoices();
    const customCheckout = await storage.getCustomCheckoutPages();
    const allowedUsernames = await storage.getAllowedUsernames();
    const emailTemplates = await storage.getEmailTemplates();
    const paypalButtons = await storage.getPaypalButtons();

    // Create hardcoded settings
    const settings = {
      general: {
        siteName: "PayPal Invoicer",
        siteDescription: "Generate and manage PayPal invoices",
        logoUrl: "",
        faviconUrl: "",
        primaryColor: "#0070ba",
        secondaryColor: "#003087",
        footerText: "© 2023 PayPal Invoicer",
        enableCheckout: true,
        enableCustomCheckout: true,
        enableTestMode: true,
        defaultTestCustomer: {
          enabled: true,
          name: "Test Customer",
          email: "<EMAIL>"
        },
        emailDomainRestriction: {
          enabled: true,
          allowedDomains: "gmail.com, hotmail.com, yahoo.com"
        }
      },
      email: {
        providers: [
          {
            id: 'smtp-1',
            name: 'Primary SMTP',
            active: true,
            isDefault: true,
            isBackup: false,
            credentials: {
              host: 'smtp-relay.example.com',
              port: '587',
              secure: false,
              auth: {
                user: '<EMAIL>',
                pass: '********'
              },
              fromEmail: '<EMAIL>',
              fromName: 'PayPal Invoicer'
            }
          }
        ]
      },
      payment: {
        providers: [
          {
            id: 'paypal',
            name: 'PayPal',
            active: true,
            config: {
              clientId: '********',
              clientSecret: '********',
              mode: 'sandbox',
              webhookId: '',
              paypalEmail: '<EMAIL>'
            }
          },
          {
            id: 'custom-link',
            name: 'Custom Payment Links',
            active: true,
            config: {
              links: [
                {
                  id: 'link-1',
                  name: 'Default Payment Required',
                  paymentLink: 'https://example.com/pay',
                  buttonText: 'Complete Payment',
                  successRedirectUrl: 'https://example.com/thank-you',
                  active: true
                }
              ],
              rotationMethod: 'round-robin',
              lastUsedIndex: 0
            }
          }
        ]
      }
    };

    // Gather all data
    const allData = {
      products,
      invoices,
      settings,
      customCheckout,
      allowedUsernames,
      emailTemplates,
      paypalButtons
    };

    // Set response headers for file download
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="complete-export-${Date.now()}.json"`);

    // Send all data as JSON
    res.json(allData);
  } catch (error) {
    console.error('Error exporting all data:', error);
    res.status(500).json({ message: 'Failed to export all data' });
  }
});

// Import products
dataExportImportRouter.post('/products', isAdmin, upload.single('importFile'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const uploadedFile = req.file;

    // Read the file
    const fileContent = fs.readFileSync(uploadedFile.path, 'utf8');

    // Parse the JSON
    const products = JSON.parse(fileContent);

    // Validate the data
    if (!Array.isArray(products)) {
      return res.status(400).json({ message: 'Invalid products data: expected an array' });
    }

    // In a real implementation, this would validate each product and import them
    // For now, we'll just simulate the import

    // Clean up the uploaded file
    fs.unlinkSync(uploadedFile.path);

    res.json({
      success: true,
      message: `Successfully imported ${products.length} products`,
      count: products.length
    });
  } catch (error) {
    console.error('Error importing products:', error);

    // Clean up the uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      message: 'Failed to import products',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Import orders
dataExportImportRouter.post('/orders', isAdmin, upload.single('importFile'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const uploadedFile = req.file;

    // Read the file
    const fileContent = fs.readFileSync(uploadedFile.path, 'utf8');

    // Parse the JSON
    const orders = JSON.parse(fileContent);

    // Validate the data
    if (!Array.isArray(orders)) {
      return res.status(400).json({ message: 'Invalid orders data: expected an array' });
    }

    // In a real implementation, this would validate each order and import them
    // For now, we'll just simulate the import

    // Clean up the uploaded file
    fs.unlinkSync(uploadedFile.path);

    res.json({
      success: true,
      message: `Successfully imported ${orders.length} orders`,
      count: orders.length
    });
  } catch (error) {
    console.error('Error importing orders:', error);

    // Clean up the uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      message: 'Failed to import orders',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Import settings
dataExportImportRouter.post('/settings', isAdmin, upload.single('importFile'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const uploadedFile = req.file;

    // Read the file
    const fileContent = fs.readFileSync(uploadedFile.path, 'utf8');

    // Parse the JSON
    const settings = JSON.parse(fileContent);

    // Validate the data
    if (typeof settings !== 'object' || settings === null) {
      return res.status(400).json({ message: 'Invalid settings data: expected an object' });
    }

    // In a real implementation, this would validate and apply the settings
    // For now, we'll just simulate the import

    // Clean up the uploaded file
    fs.unlinkSync(uploadedFile.path);

    res.json({
      success: true,
      message: 'Settings imported successfully'
    });
  } catch (error) {
    console.error('Error importing settings:', error);

    // Clean up the uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      message: 'Failed to import settings',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Import all data
dataExportImportRouter.post('/all', isAdmin, upload.single('importFile'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const uploadedFile = req.file;

    // Read the file
    const fileContent = fs.readFileSync(uploadedFile.path, 'utf8');

    // Parse the JSON
    const allData = JSON.parse(fileContent);

    // Validate the data
    if (typeof allData !== 'object' || allData === null) {
      return res.status(400).json({ message: 'Invalid data: expected an object' });
    }

    // In a real implementation, this would validate and import all the data
    // For now, we'll just simulate the import

    // Clean up the uploaded file
    fs.unlinkSync(uploadedFile.path);

    res.json({
      success: true,
      message: 'All data imported successfully'
    });
  } catch (error) {
    console.error('Error importing all data:', error);

    // Clean up the uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      message: 'Failed to import all data',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { dataExportImportRouter };
