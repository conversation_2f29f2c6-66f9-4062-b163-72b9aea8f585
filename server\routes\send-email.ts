import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { sendCustomEmail } from '../services/email';
import { configStorage } from '../config-storage';

export const sendEmailRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  console.log('Checking admin session:', req.session);
  if (req.session && req.session.isAdmin) {
    console.log('Admin session verified:', req.session.isAdmin);
    next();
  } else {
    console.log('Admin session verification failed');
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Schema for sending emails
const sendEmailSchema = z.object({
  orderId: z.number().optional(),
  to: z.string().email('Valid email is required'),
  subject: z.string().min(1, 'Subject is required'),
  content: z.string().min(1, 'Content is required'),
  smtpProviderId: z.string().optional(),
  addToAllowedEmails: z.boolean().optional().default(false)
});

// Test endpoint to verify the router is working
sendEmailRouter.get('/test', (req: Request, res: Response) => {
  res.json({ message: 'Send email router is working!' });
});

// Send an email
sendEmailRouter.post('/send', checkAdmin, async (req: Request, res: Response) => {
  try {
    console.log('Received request to send email:', req.body);
    const validatedData = sendEmailSchema.parse(req.body);

    // No longer extracting links, M3U links, usernames, or passwords from email content

    // Actually send the email using the email service
    console.log('Sending email to:', validatedData.to);
    console.log('Subject:', validatedData.subject);
    console.log('SMTP Provider:', validatedData.smtpProviderId || 'default');

    let emailResult;
    try {
      // Send the email
      emailResult = await sendCustomEmail(
        validatedData.to,
        validatedData.subject,
        validatedData.content
      );

      console.log('Email sent successfully:', emailResult);
    } catch (emailError) {
      console.error('Error sending email:', emailError);
      throw new Error(`Failed to send email: ${emailError.message}`);
    }

    // Update the order with the email sent information if orderId is provided
    if (validatedData.orderId) {
      const order = await storage.getInvoice(validatedData.orderId);

      if (order) {
        const notes = order.notes
          ? `${order.notes}\n\nEmail sent on ${new Date().toISOString()} - Subject: ${validatedData.subject}`
          : `Email sent on ${new Date().toISOString()} - Subject: ${validatedData.subject}`;

        await storage.updateInvoice(validatedData.orderId, { notes });
      }
    }

    // Add or update the email in the allowed emails list if requested
    let allowedEmailResult = null;
    if (validatedData.addToAllowedEmails) {
      try {
        // Get the order details if available
        let customerName = '';
        let customerCountry = '';

        if (validatedData.orderId) {
          const order = await storage.getInvoice(validatedData.orderId);
          if (order) {
            customerName = order.customerName || '';
            customerCountry = order.country || '';
          }
        }

        // Get the SMTP provider name if available
        let smtpProviderName = '';
        let smtpProviderId = validatedData.smtpProviderId || '';

        if (smtpProviderId) {
          const provider = configStorage.email.providers.find(p => p.id === smtpProviderId);
          if (provider) {
            smtpProviderName = provider.name;
          }
        }

        // Prepare notes with customer info and SMTP provider only (no extracted credentials)
        let notes = '';

        if (customerName) {
          notes += `Customer: ${customerName}\n`;
        }

        if (customerCountry) {
          notes += `Country: ${customerCountry}\n`;
        }

        if (smtpProviderName) {
          notes += `SMTP Provider: ${smtpProviderName}\n`;
        } else if (smtpProviderId) {
          notes += `SMTP Provider: ${smtpProviderId}\n`;
        }

        // Add or update the email in the allowed list
        allowedEmailResult = await storage.updateOrCreateAllowedEmail(validatedData.to, {
          notes: notes.trim(),
          smtpProvider: smtpProviderName || smtpProviderId || '',
          lastUpdated: new Date().toISOString()
        });

        console.log('Email added/updated in allowed list:', allowedEmailResult);
      } catch (allowedEmailError) {
        console.error('Error adding email to allowed list:', allowedEmailError);
        // Don't fail the whole request if this part fails
      }
    }

    res.json({
      message: 'Email sent successfully',
      emailResult,
      allowedEmailResult
    });
  } catch (error) {
    console.error('Error sending email:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
