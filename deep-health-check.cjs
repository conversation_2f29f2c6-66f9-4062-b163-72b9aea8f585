const axios = require('axios');
const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001';
const ADMIN_TOKEN = 's3cur3-4dm1n-4cc3ss-t0k3n';

console.log('🔬 DEEP SYSTEM HEALTH CHECK - LEVEL 2');
console.log('=====================================\n');

let healthReport = {
  database: { status: 'unknown', issues: [], warnings: [] },
  api: { status: 'unknown', issues: [], warnings: [] },
  security: { status: 'unknown', issues: [], warnings: [] },
  performance: { status: 'unknown', issues: [], warnings: [] },
  integration: { status: 'unknown', issues: [], warnings: [] },
  overall: { status: 'unknown', score: 0, criticalIssues: 0 }
};

// Deep Database Analysis
async function deepDatabaseCheck() {
  console.log('🗄️  DEEP DATABASE ANALYSIS');
  console.log('==========================');
  
  try {
    const db = new Database('./data.db');
    
    // Check database integrity
    console.log('🔍 Checking database integrity...');
    const integrityCheck = db.prepare('PRAGMA integrity_check').get();
    if (integrityCheck.integrity_check !== 'ok') {
      healthReport.database.issues.push(`Database integrity check failed: ${integrityCheck.integrity_check}`);
    } else {
      console.log('✅ Database integrity: OK');
    }
    
    // Check foreign key constraints
    console.log('🔍 Checking foreign key constraints...');
    const foreignKeyCheck = db.prepare('PRAGMA foreign_key_check').all();
    if (foreignKeyCheck.length > 0) {
      healthReport.database.issues.push(`Foreign key violations found: ${foreignKeyCheck.length}`);
    } else {
      console.log('✅ Foreign key constraints: OK');
    }
    
    // Analyze table schemas
    console.log('🔍 Analyzing table schemas...');
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
    
    for (const table of tables) {
      const tableName = table.name;
      const tableInfo = db.prepare(`PRAGMA table_info(${tableName})`).all();
      const indexInfo = db.prepare(`PRAGMA index_list(${tableName})`).all();
      
      console.log(`📋 Table: ${tableName}`);
      console.log(`   Columns: ${tableInfo.length}`);
      console.log(`   Indexes: ${indexInfo.length}`);
      
      // Check for missing primary keys
      const hasPrimaryKey = tableInfo.some(col => col.pk === 1);
      if (!hasPrimaryKey) {
        healthReport.database.warnings.push(`Table ${tableName} has no primary key`);
      }
      
      // Check for nullable required fields
      const nullableFields = tableInfo.filter(col => col.notnull === 0 && col.name !== 'id');
      if (nullableFields.length > tableInfo.length * 0.8) {
        healthReport.database.warnings.push(`Table ${tableName} has many nullable fields`);
      }
    }
    
    // Check database size and performance
    const dbStats = fs.statSync('./data.db');
    console.log(`📊 Database size: ${(dbStats.size / 1024).toFixed(2)} KB`);
    
    if (dbStats.size > 100 * 1024 * 1024) { // 100MB
      healthReport.database.warnings.push('Database size is large (>100MB)');
    }
    
    // Test database performance
    console.log('🔍 Testing database performance...');
    const startTime = Date.now();
    db.prepare('SELECT COUNT(*) FROM users').get();
    db.prepare('SELECT COUNT(*) FROM products').get();
    db.prepare('SELECT COUNT(*) FROM invoices').get();
    const queryTime = Date.now() - startTime;
    
    console.log(`⚡ Query performance: ${queryTime}ms`);
    if (queryTime > 1000) {
      healthReport.database.warnings.push(`Slow database queries detected: ${queryTime}ms`);
    }
    
    db.close();
    healthReport.database.status = healthReport.database.issues.length === 0 ? 'healthy' : 'issues';
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
    healthReport.database.issues.push(`Database check failed: ${error.message}`);
    healthReport.database.status = 'critical';
  }
}

// Deep API Testing
async function deepAPICheck() {
  console.log('\n🌐 DEEP API ANALYSIS');
  console.log('====================');
  
  let adminSession = null;
  
  try {
    // Login and get session
    console.log('🔐 Authenticating admin session...');
    const loginResponse = await axios.post(`${BASE_URL}/api/admin/login?token=${ADMIN_TOKEN}`, {
      username: 'admin',
      password: 'admin123'
    });
    
    const cookies = loginResponse.headers['set-cookie'];
    if (cookies) {
      adminSession = cookies.find(cookie => cookie.startsWith('connect.sid'));
    }
    
    if (!adminSession) {
      healthReport.api.issues.push('Failed to establish admin session');
    }
    
    // Test all API endpoints with different scenarios
    const apiTests = [
      // Public endpoints
      { method: 'GET', path: '/api/health', name: 'Health Check', public: true },
      { method: 'GET', path: '/api/products', name: 'Products List', public: true },
      { method: 'GET', path: '/api/system-messages', name: 'System Messages', public: true },
      { method: 'GET', path: '/api/homepage', name: 'Homepage Config', public: true },
      
      // Admin endpoints
      { method: 'GET', path: '/api/admin/stats', name: 'Admin Stats', admin: true },
      { method: 'GET', path: '/api/admin/products', name: 'Admin Products', admin: true },
      { method: 'GET', path: '/api/admin/invoices', name: 'Admin Invoices', admin: true },
      { method: 'GET', path: '/api/admin/email-config', name: 'Email Config', admin: true },
      { method: 'GET', path: '/api/admin/payment-config', name: 'Payment Config', admin: true },
      
      // Protected endpoints
      { method: 'GET', path: '/api/smtp-providers', name: 'SMTP Providers', protected: true },
      { method: 'GET', path: '/api/email-templates', name: 'Email Templates', protected: true },
      { method: 'GET', path: '/api/custom-checkout-pages', name: 'Custom Checkout', protected: true },
      { method: 'GET', path: '/api/invoices', name: 'Invoices', protected: true },
      { method: 'GET', path: '/api/paypal-buttons', name: 'PayPal Buttons', protected: true },
      { method: 'GET', path: '/api/general-settings', name: 'General Settings', protected: true },
    ];
    
    for (const test of apiTests) {
      try {
        console.log(`🔍 Testing: ${test.name}`);
        
        let config = { timeout: 5000 };
        let url = `${BASE_URL}${test.path}`;
        
        if (test.admin) {
          url += `?token=${ADMIN_TOKEN}`;
          if (adminSession) {
            config.headers = { Cookie: adminSession };
          }
        } else if (test.protected) {
          if (adminSession) {
            config.headers = { Cookie: adminSession };
          }
        }
        
        const response = await axios({
          method: test.method,
          url: url,
          ...config,
          validateStatus: () => true // Accept any status
        });
        
        // Analyze response
        if (test.public && response.status >= 400) {
          healthReport.api.issues.push(`Public endpoint ${test.name} failed: ${response.status}`);
        } else if (test.protected && response.status === 401) {
          // Expected for protected endpoints without auth
          console.log(`   ✅ ${test.name}: Properly protected (401)`);
        } else if (test.admin && response.status >= 400) {
          healthReport.api.issues.push(`Admin endpoint ${test.name} failed: ${response.status}`);
        } else if (response.status < 400) {
          console.log(`   ✅ ${test.name}: OK (${response.status})`);
          
          // Check response structure
          if (response.data && typeof response.data === 'object') {
            if (Array.isArray(response.data) && response.data.length === 0) {
              healthReport.api.warnings.push(`${test.name} returns empty array`);
            }
          }
        }
        
        // Check response time
        const responseTime = response.headers['x-response-time'];
        if (responseTime && parseInt(responseTime) > 1000) {
          healthReport.api.warnings.push(`${test.name} slow response: ${responseTime}ms`);
        }
        
      } catch (error) {
        if (error.code === 'ECONNREFUSED') {
          healthReport.api.issues.push('Server connection refused');
          break;
        } else {
          healthReport.api.issues.push(`${test.name} error: ${error.message}`);
        }
      }
    }
    
    healthReport.api.status = healthReport.api.issues.length === 0 ? 'healthy' : 'issues';
    
  } catch (error) {
    console.error('❌ API check failed:', error.message);
    healthReport.api.issues.push(`API check failed: ${error.message}`);
    healthReport.api.status = 'critical';
  }
}

// Security Analysis
async function securityCheck() {
  console.log('\n🔒 SECURITY ANALYSIS');
  console.log('====================');
  
  try {
    // Test unauthorized access
    console.log('🔍 Testing unauthorized access protection...');
    const unauthorizedTests = [
      '/api/admin/stats',
      '/api/admin/products',
      '/api/admin/email-config',
      '/api/smtp-providers',
      '/api/general-settings'
    ];
    
    for (const endpoint of unauthorizedTests) {
      try {
        const response = await axios.get(`${BASE_URL}${endpoint}`, { timeout: 3000 });
        if (response.status < 400) {
          healthReport.security.issues.push(`Endpoint ${endpoint} accessible without authentication`);
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          console.log(`   ✅ ${endpoint}: Properly protected`);
        }
      }
    }
    
    // Test admin token requirement
    console.log('🔍 Testing admin token protection...');
    try {
      const response = await axios.post(`${BASE_URL}/api/admin/login`, {
        username: 'admin',
        password: 'admin123'
      });
      if (response.status < 400) {
        healthReport.security.issues.push('Admin login accessible without token');
      }
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('   ✅ Admin login requires token');
      }
    }
    
    // Check environment variables
    console.log('🔍 Checking security configuration...');
    if (!process.env.SESSION_SECRET || process.env.SESSION_SECRET === 'local-development-secret') {
      healthReport.security.warnings.push('Using default session secret');
    }
    
    if (!process.env.ADMIN_ACCESS_TOKEN || process.env.ADMIN_ACCESS_TOKEN.length < 20) {
      healthReport.security.warnings.push('Weak admin access token');
    }
    
    healthReport.security.status = healthReport.security.issues.length === 0 ? 'healthy' : 'issues';
    
  } catch (error) {
    console.error('❌ Security check failed:', error.message);
    healthReport.security.issues.push(`Security check failed: ${error.message}`);
    healthReport.security.status = 'critical';
  }
}

// Performance Analysis
async function performanceCheck() {
  console.log('\n⚡ PERFORMANCE ANALYSIS');
  console.log('=======================');
  
  try {
    // Test response times
    console.log('🔍 Testing response times...');
    const performanceTests = [
      { path: '/', name: 'Homepage' },
      { path: '/api/health', name: 'Health API' },
      { path: '/api/products', name: 'Products API' },
      { path: '/api/system-messages', name: 'System Messages' }
    ];
    
    for (const test of performanceTests) {
      const startTime = Date.now();
      try {
        await axios.get(`${BASE_URL}${test.path}`, { timeout: 10000 });
        const responseTime = Date.now() - startTime;
        console.log(`   ${test.name}: ${responseTime}ms`);
        
        if (responseTime > 2000) {
          healthReport.performance.issues.push(`${test.name} slow response: ${responseTime}ms`);
        } else if (responseTime > 1000) {
          healthReport.performance.warnings.push(`${test.name} moderate response: ${responseTime}ms`);
        }
      } catch (error) {
        healthReport.performance.issues.push(`${test.name} failed: ${error.message}`);
      }
    }
    
    // Check file system
    console.log('🔍 Checking file system...');
    const uploadsDir = './uploads';
    if (!fs.existsSync(uploadsDir)) {
      healthReport.performance.warnings.push('Uploads directory does not exist');
    }
    
    healthReport.performance.status = healthReport.performance.issues.length === 0 ? 'healthy' : 'issues';
    
  } catch (error) {
    console.error('❌ Performance check failed:', error.message);
    healthReport.performance.issues.push(`Performance check failed: ${error.message}`);
    healthReport.performance.status = 'critical';
  }
}

// Integration Testing
async function integrationCheck() {
  console.log('\n🔗 INTEGRATION ANALYSIS');
  console.log('=======================');
  
  try {
    // Test email system
    console.log('🔍 Testing email system integration...');
    // Note: We won't actually send emails in health check
    
    // Test Telegram integration
    console.log('🔍 Testing Telegram integration...');
    // Note: We won't send actual Telegram messages
    
    // Test PayPal integration readiness
    console.log('🔍 Testing PayPal integration readiness...');
    // Check if PayPal credentials are configured
    
    healthReport.integration.status = 'healthy';
    
  } catch (error) {
    console.error('❌ Integration check failed:', error.message);
    healthReport.integration.issues.push(`Integration check failed: ${error.message}`);
    healthReport.integration.status = 'critical';
  }
}

// Generate Overall Score
function generateOverallScore() {
  const components = ['database', 'api', 'security', 'performance', 'integration'];
  let totalScore = 0;
  let criticalIssues = 0;
  
  components.forEach(component => {
    const comp = healthReport[component];
    if (comp.status === 'healthy') {
      totalScore += 20;
    } else if (comp.status === 'issues') {
      totalScore += 10;
    }
    
    criticalIssues += comp.issues.length;
  });
  
  healthReport.overall.score = totalScore;
  healthReport.overall.criticalIssues = criticalIssues;
  
  if (criticalIssues === 0 && totalScore >= 90) {
    healthReport.overall.status = 'excellent';
  } else if (criticalIssues === 0 && totalScore >= 70) {
    healthReport.overall.status = 'healthy';
  } else if (criticalIssues <= 2) {
    healthReport.overall.status = 'warning';
  } else {
    healthReport.overall.status = 'critical';
  }
}

// Main execution
async function runDeepHealthCheck() {
  try {
    await deepDatabaseCheck();
    await deepAPICheck();
    await securityCheck();
    await performanceCheck();
    await integrationCheck();
    
    generateOverallScore();
    
    // Print final report
    console.log('\n📊 DEEP HEALTH CHECK RESULTS');
    console.log('=============================');
    
    const getStatusIcon = (status) => {
      switch (status) {
        case 'excellent': return '🌟';
        case 'healthy': return '✅';
        case 'warning': return '⚠️ ';
        case 'issues': return '🔶';
        case 'critical': return '🚨';
        default: return '❓';
      }
    };
    
    console.log(`${getStatusIcon(healthReport.database.status)} Database: ${healthReport.database.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.api.status)} API: ${healthReport.api.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.security.status)} Security: ${healthReport.security.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.performance.status)} Performance: ${healthReport.performance.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.integration.status)} Integration: ${healthReport.integration.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.overall.status)} Overall: ${healthReport.overall.status.toUpperCase()} (${healthReport.overall.score}%)`);
    
    // Print all issues and warnings
    const allIssues = [
      ...healthReport.database.issues,
      ...healthReport.api.issues,
      ...healthReport.security.issues,
      ...healthReport.performance.issues,
      ...healthReport.integration.issues
    ];
    
    const allWarnings = [
      ...healthReport.database.warnings,
      ...healthReport.api.warnings,
      ...healthReport.security.warnings,
      ...healthReport.performance.warnings,
      ...healthReport.integration.warnings
    ];
    
    if (allIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES:');
      allIssues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    if (allWarnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      allWarnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning}`);
      });
    }
    
    if (allIssues.length === 0 && allWarnings.length === 0) {
      console.log('\n🎉 NO ISSUES OR WARNINGS FOUND!');
      console.log('🌟 SYSTEM IS IN EXCELLENT CONDITION!');
    }
    
    // Save detailed report
    fs.writeFileSync('deep-health-report.json', JSON.stringify(healthReport, null, 2));
    console.log('\n📄 Detailed report saved to: deep-health-report.json');
    
  } catch (error) {
    console.error('🚨 Deep health check failed:', error);
    process.exit(1);
  }
}

// Run the deep health check
runDeepHealthCheck();
