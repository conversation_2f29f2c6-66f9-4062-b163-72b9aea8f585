import { useState, useRef, ChangeEvent, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Upload, Image, X, Loader2, FolderOpen } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import ImageBrowser from "./ImageBrowser";

interface EnhancedImageUploaderProps {
  initialUrl?: string;
  onImageUploaded: (url: string) => void;
  label?: string;
}

export default function EnhancedImageUploader({ 
  initialUrl, 
  onImageUploaded,
  label = "Product Image" 
}: EnhancedImageUploaderProps) {
  const [imageUrl, setImageUrl] = useState<string>(initialUrl || "");
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Update imageUrl when initialUrl prop changes
  useEffect(() => {
    setImageUrl(initialUrl || "");
  }, [initialUrl]);

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await uploadFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    const file = e.dataTransfer.files?.[0];
    if (file) {
      await uploadFile(file);
    }
  };

  const uploadFile = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();
      setImageUrl(data.url);
      onImageUploaded(data.url);

      toast({
        title: "Image uploaded",
        description: "The image was uploaded successfully"
      });
    } catch (error) {
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setImageUrl("");
    onImageUploaded("");
  };

  const handleImageSelected = (url: string) => {
    setImageUrl(url);
    onImageUploaded(url);
  };

  return (
    <div className="space-y-2">
      <Label htmlFor="product-image">{label}</Label>

      {!imageUrl ? (
        <Tabs defaultValue="upload" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload New</TabsTrigger>
            <TabsTrigger value="browse">Browse Existing</TabsTrigger>
          </TabsList>
          
          <TabsContent value="upload" className="mt-4">
            <div
              className={`border-2 border-dashed rounded-md p-6 text-center cursor-pointer transition-colors ${
                isDragging ? "border-primary bg-primary/5" : "border-gray-300 hover:border-primary/50"
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                type="file"
                id="product-image"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={handleFileChange}
              />

              {isUploading ? (
                <div className="flex flex-col items-center">
                  <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                  <p className="text-sm text-gray-600">Uploading image...</p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <Upload className="h-8 w-8 text-gray-400 mb-2" />
                  <p className="text-sm text-gray-600 mb-1">
                    Drag and drop an image here, or click to select
                  </p>
                  <p className="text-xs text-gray-500">
                    Supports JPEG, PNG, GIF, and WebP (max 5MB)
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="browse" className="mt-4">
            <div className="border-2 border-dashed rounded-md p-6 text-center">
              <div className="flex flex-col items-center">
                <FolderOpen className="h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600 mb-4">
                  Choose from previously uploaded images
                </p>
                <ImageBrowser 
                  onImageSelected={handleImageSelected}
                  trigger={
                    <Button variant="outline">
                      <Image className="h-4 w-4 mr-2" />
                      Browse Images
                    </Button>
                  }
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      ) : (
        <div className="relative aspect-video w-full max-w-md overflow-hidden rounded-md border">
          <img 
            src={imageUrl} 
            alt="Selected image" 
            className="h-full w-full object-cover"
          />
          <Button
            variant="destructive"
            size="icon"
            className="absolute top-2 right-2 h-7 w-7 rounded-full opacity-90"
            onClick={handleRemoveImage}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
