{"timestamp": "2025-06-03T14:06:11.440Z", "database": {"status": "healthy", "issues": [], "warnings": [], "data": {"users": {"count": 1, "hasData": true, "sample": {"id": 1, "username": "admin", "password": "admin123"}}, "products": {"count": 3, "hasData": true, "sample": {"id": 1, "name": "Dashboard Pro Template", "description": "Professional dashboard template with modern design", "price": "29.99", "image_url": "/api/placeholder/400/300", "active": 1}}, "invoices": {"count": 0, "hasData": false, "sample": null}, "custom_checkout_pages": {"count": 0, "hasData": false, "sample": null}, "allowed_emails": {"count": 0, "hasData": false, "sample": null}, "smtp_providers": {"count": 2, "hasData": true, "sample": {"id": "smtp-1", "name": "Enzidswan", "host": "smtp-relay.brevo.com", "port": "587", "secure": 0, "username": "<EMAIL>", "password": "3d8I9xFm1yMDYj7W", "from_email": "<EMAIL>", "from_name": "PayPal Invoicer", "active": 1, "is_default": 1, "is_backup": 0, "created_at": "2025-06-03T13:21:32.886Z", "updated_at": "2025-06-03T13:38:32.198Z"}}, "email_templates": {"count": 0, "hasData": false, "sample": null}, "paypal_buttons": {"count": 0, "hasData": false, "sample": null}, "custom_invoices": {"count": 0, "hasData": false, "sample": null}}}, "api": {"status": "healthy", "issues": [], "warnings": [], "endpoints": {"Health Check": {"status": 200, "responseTime": 3, "dataSize": 119, "hasData": true}, "Products List": {"status": 200, "responseTime": 3, "dataSize": 2025, "hasData": true}, "System Messages": {"status": 200, "responseTime": 2, "dataSize": 3348, "hasData": true}, "Homepage Config": {"status": 200, "responseTime": 3, "dataSize": 3827, "hasData": true}, "Admin Stats": {"status": 200, "responseTime": 4, "dataSize": 98, "hasData": true}, "Admin Products": {"status": 200, "responseTime": 4, "dataSize": 2025, "hasData": true}, "Admin Invoices": {"status": 200, "responseTime": 6, "dataSize": 657, "hasData": true}, "Email Config": {"status": 200, "responseTime": 5, "dataSize": 598, "hasData": true}, "Payment Config": {"status": 200, "responseTime": 6, "dataSize": 7443, "hasData": true}, "General Settings": {"status": 404, "responseTime": 5, "dataSize": 181, "hasData": true}, "Backup List": {"status": 404, "responseTime": 4, "dataSize": 176, "hasData": true}, "SMTP Providers": {"status": 200, "responseTime": 3, "dataSize": 584, "hasData": true}, "Email Templates": {"status": 200, "responseTime": 2, "dataSize": 552, "hasData": true}, "Custom Checkout": {"status": 200, "responseTime": 3, "dataSize": 1418, "hasData": true}, "Invoices": {"status": 200, "responseTime": 2, "dataSize": 525, "hasData": true}, "PayPal Buttons": {"status": 200, "responseTime": 2, "dataSize": 2142, "hasData": true}, "General Settings Protected": {"status": 200, "responseTime": 2, "dataSize": 2811, "hasData": true}}}, "frontend": {"status": "healthy", "issues": [], "warnings": []}, "functionality": {"status": "issues", "issues": ["Backup system error: Request failed with status code 404"], "warnings": [], "features": {"productManagement": {"working": true, "productCount": 6}, "invoiceSystem": {"working": true, "invoiceCount": 1}, "emailSystem": {"working": true, "configured": true}, "paymentSystem": {"working": true}}}, "dataIntegrity": {"status": "healthy", "issues": [], "warnings": []}, "runtime": {"status": "healthy", "issues": [], "warnings": [], "logs": []}, "overall": {"status": "warning", "score": 88, "criticalIssues": 1}}