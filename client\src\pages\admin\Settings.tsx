import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import AdminLayout from "@/components/admin/AdminLayout";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Link } from "wouter";
import { Mail, CreditCard, Settings, RefreshCw, Database, MessageSquare, FileText, Shield, Bot } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function AdminSettings() {
  return (
    <AdminLayout>
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold">Settings</CardTitle>
          <CardDescription>
            Configure your application settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview">
            <TabsList className="mb-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {/* Email settings card */}
                <Card>
                  <CardHeader className="p-5">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-medium">Email</CardTitle>
                      <Mail className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <CardDescription>
                      Configure your email service providers
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-5 pt-0">
                    <p className="text-sm text-muted-foreground mb-5">
                      Set up email notifications for orders and customers
                    </p>
                    <Button asChild>
                      <Link href="/admin/email-settings">
                        Manage Email Settings
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                {/* Payment settings card */}
                <Card>
                  <CardHeader className="p-5">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-medium">Payments</CardTitle>
                      <CreditCard className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <CardDescription>
                      Configure your payment providers
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-5 pt-0">
                    <p className="text-sm text-muted-foreground mb-5">
                      Manage PayPal and other payment settings
                    </p>
                    <Button asChild>
                      <Link href="/admin/payment-settings">
                        Manage Payment Settings
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                {/* General settings card */}
                <Card>
                  <CardHeader className="p-5">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-medium">General</CardTitle>
                      <Settings className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <CardDescription>
                      Application general settings
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-5 pt-0">
                    <p className="text-sm text-muted-foreground mb-5">
                      Configure store name, branding, and site preferences
                    </p>
                    <Button asChild>
                      <Link href="/admin/general-settings">
                        Manage General Settings
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                {/* SEO Privacy settings card */}
                <Card>
                  <CardHeader className="p-5">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-medium">SEO & Privacy</CardTitle>
                      <Shield className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <CardDescription>
                      Control search engine visibility and privacy
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-5 pt-0">
                    <p className="text-sm text-muted-foreground mb-5">
                      Hide from search engines, manage robots.txt, and protect against fraud buyers
                    </p>
                    <Button asChild>
                      <Link href="/admin/seo-privacy-settings">
                        Manage SEO & Privacy
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                {/* Telegram Bot settings card */}
                <Card>
                  <CardHeader className="p-5">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-medium">Telegram Bot</CardTitle>
                      <Bot className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <CardDescription>
                      Order notifications and management via Telegram
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-5 pt-0">
                    <p className="text-sm text-muted-foreground mb-5">
                      Get instant order notifications, send emails, and manage M3U links from Telegram
                    </p>
                    <Button asChild>
                      <Link href="/admin/telegram-bot-settings">
                        Configure Telegram Bot
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                {/* System Updates card */}
                <Card className="mt-6">
                  <CardHeader className="p-5">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-medium">System Updates & Backups</CardTitle>
                      <RefreshCw className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <CardDescription>
                      Manage application updates and data backups
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-5 pt-0">
                    <p className="text-sm text-muted-foreground mb-5">
                      Update your application, create backups, and export/import data
                    </p>
                    <Button asChild>
                      <Link href="/admin/system-updates">
                        Manage System Updates
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                {/* System Messages card */}
                <Card className="mt-6">
                  <CardHeader className="p-5">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-medium">System Messages</CardTitle>
                      <MessageSquare className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <CardDescription>
                      Customize system messages and notifications
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-5 pt-0">
                    <p className="text-sm text-muted-foreground mb-5">
                      Edit error messages, notifications, and other text displayed to users
                    </p>
                    <Button asChild>
                      <Link href="/admin/system-messages">
                        Manage System Messages
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                {/* Email Templates card */}
                <Card className="mt-6">
                  <CardHeader className="p-5">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-medium">Email Templates</CardTitle>
                      <FileText className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <CardDescription>
                      Customize email templates for all communications
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-5 pt-0">
                    <p className="text-sm text-muted-foreground mb-5">
                      Edit email templates for purchase confirmations, notifications, and manual communications
                    </p>
                    <Button asChild>
                      <Link href="/admin/email-templates">
                        Manage Email Templates
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </AdminLayout>
  );
}