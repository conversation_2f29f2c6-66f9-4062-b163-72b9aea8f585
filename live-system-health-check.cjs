const axios = require('axios');
const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001';
const ADMIN_TOKEN = 's3cur3-4dm1n-4cc3ss-t0k3n';

console.log('🔍 LIVE SYSTEM HEALTH CHECK - WITH REAL DATA');
console.log('=============================================\n');

let healthReport = {
  timestamp: new Date().toISOString(),
  database: { status: 'unknown', issues: [], warnings: [], data: {} },
  api: { status: 'unknown', issues: [], warnings: [], endpoints: {} },
  frontend: { status: 'unknown', issues: [], warnings: [] },
  functionality: { status: 'unknown', issues: [], warnings: [], features: {} },
  dataIntegrity: { status: 'unknown', issues: [], warnings: [] },
  runtime: { status: 'unknown', issues: [], warnings: [], logs: [] },
  overall: { status: 'unknown', score: 0, criticalIssues: 0 }
};

// Live Database Analysis with Real Data
async function liveDatabaseCheck() {
  console.log('🗄️  LIVE DATABASE ANALYSIS WITH REAL DATA');
  console.log('==========================================');
  
  try {
    const db = new Database('./data.db');
    
    // Check all tables and their actual data
    const tables = [
      'users', 'products', 'invoices', 'custom_checkout_pages',
      'allowed_emails', 'smtp_providers', 'email_templates',
      'paypal_buttons', 'custom_invoices'
    ];
    
    console.log('📊 Analyzing table data...');
    for (const tableName of tables) {
      try {
        const count = db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get();
        const sample = db.prepare(`SELECT * FROM ${tableName} LIMIT 3`).all();
        
        healthReport.database.data[tableName] = {
          count: count.count,
          hasData: count.count > 0,
          sample: sample.length > 0 ? sample[0] : null
        };
        
        console.log(`   ${tableName}: ${count.count} records`);
        
        // Check for data quality issues
        if (tableName === 'users' && count.count === 0) {
          healthReport.database.issues.push('No users found - system cannot be accessed');
        }
        
        if (tableName === 'products' && count.count === 0) {
          healthReport.database.warnings.push('No products found - e-commerce functionality limited');
        }
        
        // Check for orphaned records
        if (tableName === 'invoices' && count.count > 0) {
          const orphanedInvoices = db.prepare(`
            SELECT COUNT(*) as count FROM invoices 
            WHERE product_id NOT IN (SELECT id FROM products)
          `).get();
          
          if (orphanedInvoices.count > 0) {
            healthReport.database.issues.push(`${orphanedInvoices.count} orphaned invoices found`);
          }
        }
        
        if (tableName === 'custom_checkout_pages' && count.count > 0) {
          const orphanedPages = db.prepare(`
            SELECT COUNT(*) as count FROM custom_checkout_pages 
            WHERE product_id NOT IN (SELECT id FROM products)
          `).get();
          
          if (orphanedPages.count > 0) {
            healthReport.database.issues.push(`${orphanedPages.count} orphaned checkout pages found`);
          }
        }
        
      } catch (error) {
        healthReport.database.issues.push(`Error checking table ${tableName}: ${error.message}`);
      }
    }
    
    // Check for data consistency
    console.log('🔍 Checking data consistency...');
    
    // Check SMTP provider configuration
    const smtpProviders = db.prepare('SELECT * FROM smtp_providers WHERE active = 1').all();
    if (smtpProviders.length === 0) {
      healthReport.database.warnings.push('No active SMTP providers - email functionality disabled');
    } else {
      console.log(`   Active SMTP providers: ${smtpProviders.length}`);
    }
    
    // Check for duplicate slugs in checkout pages
    const duplicateSlugs = db.prepare(`
      SELECT slug, COUNT(*) as count 
      FROM custom_checkout_pages 
      GROUP BY slug 
      HAVING COUNT(*) > 1
    `).all();
    
    if (duplicateSlugs.length > 0) {
      healthReport.database.issues.push(`Duplicate checkout page slugs found: ${duplicateSlugs.length}`);
    }
    
    db.close();
    healthReport.database.status = healthReport.database.issues.length === 0 ? 'healthy' : 'issues';
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message);
    healthReport.database.issues.push(`Database check failed: ${error.message}`);
    healthReport.database.status = 'critical';
  }
}

// Live API Testing with Real Data
async function liveAPICheck() {
  console.log('\n🌐 LIVE API TESTING WITH REAL DATA');
  console.log('===================================');
  
  let adminSession = null;
  
  try {
    // Authenticate admin session
    console.log('🔐 Authenticating admin session...');
    const loginResponse = await axios.post(`${BASE_URL}/api/admin/login?token=${ADMIN_TOKEN}`, {
      username: 'admin',
      password: 'admin123'
    });
    
    const cookies = loginResponse.headers['set-cookie'];
    if (cookies) {
      adminSession = cookies.find(cookie => cookie.startsWith('connect.sid'));
    }
    
    // Test all API endpoints with real data
    const apiTests = [
      // Public endpoints
      { method: 'GET', path: '/api/health', name: 'Health Check', public: true },
      { method: 'GET', path: '/api/products', name: 'Products List', public: true },
      { method: 'GET', path: '/api/system-messages', name: 'System Messages', public: true },
      { method: 'GET', path: '/api/homepage', name: 'Homepage Config', public: true },
      
      // Admin endpoints with authentication
      { method: 'GET', path: '/api/admin/stats', name: 'Admin Stats', admin: true },
      { method: 'GET', path: '/api/admin/products', name: 'Admin Products', admin: true },
      { method: 'GET', path: '/api/admin/invoices', name: 'Admin Invoices', admin: true },
      { method: 'GET', path: '/api/admin/email-config', name: 'Email Config', admin: true },
      { method: 'GET', path: '/api/admin/payment-config', name: 'Payment Config', admin: true },
      { method: 'GET', path: '/api/admin/general-settings', name: 'General Settings', admin: true },
      { method: 'GET', path: '/api/admin/backup-list', name: 'Backup List', admin: true },
      
      // Protected endpoints
      { method: 'GET', path: '/api/smtp-providers', name: 'SMTP Providers', protected: true },
      { method: 'GET', path: '/api/email-templates', name: 'Email Templates', protected: true },
      { method: 'GET', path: '/api/custom-checkout-pages', name: 'Custom Checkout', protected: true },
      { method: 'GET', path: '/api/invoices', name: 'Invoices', protected: true },
      { method: 'GET', path: '/api/paypal-buttons', name: 'PayPal Buttons', protected: true },
      { method: 'GET', path: '/api/general-settings', name: 'General Settings Protected', protected: true },
    ];
    
    for (const test of apiTests) {
      try {
        console.log(`🔍 Testing: ${test.name}`);
        
        let config = { timeout: 10000, validateStatus: () => true };
        let url = `${BASE_URL}${test.path}`;
        
        if (test.admin) {
          url += `?token=${ADMIN_TOKEN}`;
          if (adminSession) {
            config.headers = { Cookie: adminSession };
          }
        } else if (test.protected) {
          if (adminSession) {
            config.headers = { Cookie: adminSession };
          }
        }
        
        const startTime = Date.now();
        const response = await axios({
          method: test.method,
          url: url,
          ...config
        });
        const responseTime = Date.now() - startTime;
        
        healthReport.api.endpoints[test.name] = {
          status: response.status,
          responseTime: responseTime,
          dataSize: JSON.stringify(response.data).length,
          hasData: response.data && (Array.isArray(response.data) ? response.data.length > 0 : Object.keys(response.data).length > 0)
        };
        
        // Analyze response
        if (response.status >= 500) {
          healthReport.api.issues.push(`${test.name} server error: ${response.status}`);
        } else if (response.status >= 400 && test.public) {
          healthReport.api.issues.push(`${test.name} client error: ${response.status}`);
        } else if (response.status < 400) {
          console.log(`   ✅ ${test.name}: OK (${response.status}, ${responseTime}ms)`);
          
          // Check response data quality
          if (response.data) {
            if (Array.isArray(response.data)) {
              if (response.data.length === 0 && test.name.includes('Products')) {
                healthReport.api.warnings.push(`${test.name} returns empty array - no products available`);
              }
            } else if (typeof response.data === 'object') {
              // Check for error messages in successful responses
              if (response.data.error || response.data.message?.includes('error')) {
                healthReport.api.warnings.push(`${test.name} contains error in response`);
              }
            }
          }
        }
        
        // Check response time
        if (responseTime > 5000) {
          healthReport.api.issues.push(`${test.name} very slow response: ${responseTime}ms`);
        } else if (responseTime > 2000) {
          healthReport.api.warnings.push(`${test.name} slow response: ${responseTime}ms`);
        }
        
      } catch (error) {
        healthReport.api.issues.push(`${test.name} error: ${error.message}`);
        console.log(`   ❌ ${test.name}: ${error.message}`);
      }
    }
    
    healthReport.api.status = healthReport.api.issues.length === 0 ? 'healthy' : 'issues';
    
  } catch (error) {
    console.error('❌ API check failed:', error.message);
    healthReport.api.issues.push(`API check failed: ${error.message}`);
    healthReport.api.status = 'critical';
  }
}

// Frontend Analysis
async function frontendCheck() {
  console.log('\n🎨 FRONTEND ANALYSIS');
  console.log('====================');
  
  try {
    // Test main page load
    console.log('🔍 Testing frontend loading...');
    const response = await axios.get(BASE_URL, { timeout: 10000 });
    
    if (response.status !== 200) {
      healthReport.frontend.issues.push(`Frontend failed to load: ${response.status}`);
    } else {
      console.log('✅ Frontend loads successfully');
      
      // Check for React app
      if (!response.data.includes('id="root"')) {
        healthReport.frontend.issues.push('React root element not found');
      }
      
      // Check for common errors in HTML
      if (response.data.includes('Cannot resolve module') || response.data.includes('404')) {
        healthReport.frontend.issues.push('Module resolution errors detected');
      }
      
      // Check for Vite dev server
      if (response.data.includes('vite') || response.data.includes('@vite')) {
        console.log('✅ Vite dev server detected');
      }
    }
    
    healthReport.frontend.status = healthReport.frontend.issues.length === 0 ? 'healthy' : 'issues';
    
  } catch (error) {
    console.error('❌ Frontend check failed:', error.message);
    healthReport.frontend.issues.push(`Frontend check failed: ${error.message}`);
    healthReport.frontend.status = 'critical';
  }
}

// Functionality Testing with Real Data
async function functionalityCheck() {
  console.log('\n🔧 FUNCTIONALITY TESTING WITH REAL DATA');
  console.log('========================================');
  
  try {
    let adminSession = null;
    
    // Get admin session
    const loginResponse = await axios.post(`${BASE_URL}/api/admin/login?token=${ADMIN_TOKEN}`, {
      username: 'admin',
      password: 'admin123'
    });
    
    const cookies = loginResponse.headers['set-cookie'];
    if (cookies) {
      adminSession = cookies.find(cookie => cookie.startsWith('connect.sid'));
    }
    
    const authConfig = adminSession ? { headers: { Cookie: adminSession } } : {};
    
    // Test product management
    console.log('🔍 Testing product management...');
    try {
      const productsResponse = await axios.get(`${BASE_URL}/api/admin/products?token=${ADMIN_TOKEN}`, authConfig);
      healthReport.functionality.features.productManagement = {
        working: productsResponse.status === 200,
        productCount: Array.isArray(productsResponse.data) ? productsResponse.data.length : 0
      };
      
      if (productsResponse.status === 200) {
        console.log(`   ✅ Product management: ${productsResponse.data.length} products found`);
      }
    } catch (error) {
      healthReport.functionality.issues.push(`Product management error: ${error.message}`);
    }
    
    // Test invoice system
    console.log('🔍 Testing invoice system...');
    try {
      const invoicesResponse = await axios.get(`${BASE_URL}/api/admin/invoices?token=${ADMIN_TOKEN}`, authConfig);
      healthReport.functionality.features.invoiceSystem = {
        working: invoicesResponse.status === 200,
        invoiceCount: Array.isArray(invoicesResponse.data) ? invoicesResponse.data.length : 0
      };
      
      if (invoicesResponse.status === 200) {
        console.log(`   ✅ Invoice system: ${invoicesResponse.data.length} invoices found`);
      }
    } catch (error) {
      healthReport.functionality.issues.push(`Invoice system error: ${error.message}`);
    }
    
    // Test email configuration
    console.log('🔍 Testing email configuration...');
    try {
      const emailResponse = await axios.get(`${BASE_URL}/api/admin/email-config?token=${ADMIN_TOKEN}`, authConfig);
      healthReport.functionality.features.emailSystem = {
        working: emailResponse.status === 200,
        configured: emailResponse.data && emailResponse.data.providers && emailResponse.data.providers.length > 0
      };
      
      if (emailResponse.status === 200) {
        console.log(`   ✅ Email system: ${emailResponse.data.providers?.length || 0} providers configured`);
      }
    } catch (error) {
      healthReport.functionality.issues.push(`Email system error: ${error.message}`);
    }
    
    // Test payment configuration
    console.log('🔍 Testing payment configuration...');
    try {
      const paymentResponse = await axios.get(`${BASE_URL}/api/admin/payment-config?token=${ADMIN_TOKEN}`, authConfig);
      healthReport.functionality.features.paymentSystem = {
        working: paymentResponse.status === 200,
        configured: paymentResponse.data && (paymentResponse.data.paypal || paymentResponse.data.stripe)
      };
      
      if (paymentResponse.status === 200) {
        console.log(`   ✅ Payment system: Configuration loaded`);
      }
    } catch (error) {
      healthReport.functionality.issues.push(`Payment system error: ${error.message}`);
    }
    
    // Test backup system
    console.log('🔍 Testing backup system...');
    try {
      const backupResponse = await axios.get(`${BASE_URL}/api/admin/backup-list?token=${ADMIN_TOKEN}`, authConfig);
      healthReport.functionality.features.backupSystem = {
        working: backupResponse.status === 200,
        backupCount: Array.isArray(backupResponse.data) ? backupResponse.data.length : 0
      };
      
      if (backupResponse.status === 200) {
        console.log(`   ✅ Backup system: ${backupResponse.data.length} backups found`);
      }
    } catch (error) {
      healthReport.functionality.issues.push(`Backup system error: ${error.message}`);
    }
    
    healthReport.functionality.status = healthReport.functionality.issues.length === 0 ? 'healthy' : 'issues';
    
  } catch (error) {
    console.error('❌ Functionality check failed:', error.message);
    healthReport.functionality.issues.push(`Functionality check failed: ${error.message}`);
    healthReport.functionality.status = 'critical';
  }
}

// Data Integrity Check
async function dataIntegrityCheck() {
  console.log('\n🔍 DATA INTEGRITY ANALYSIS');
  console.log('==========================');
  
  try {
    const db = new Database('./data.db');
    
    // Check for data corruption
    console.log('🔍 Checking data integrity...');
    const integrityCheck = db.prepare('PRAGMA integrity_check').get();
    if (integrityCheck.integrity_check !== 'ok') {
      healthReport.dataIntegrity.issues.push(`Database integrity failed: ${integrityCheck.integrity_check}`);
    }
    
    // Check for missing required data
    const adminUsers = db.prepare('SELECT COUNT(*) as count FROM users WHERE username = ?').get('admin');
    if (adminUsers.count === 0) {
      healthReport.dataIntegrity.issues.push('Admin user not found');
    }
    
    // Check for invalid email addresses
    const invalidEmails = db.prepare(`
      SELECT COUNT(*) as count FROM invoices 
      WHERE customer_email NOT LIKE '%@%' OR customer_email NOT LIKE '%.%'
    `).get();
    
    if (invalidEmails.count > 0) {
      healthReport.dataIntegrity.warnings.push(`${invalidEmails.count} invoices with invalid email addresses`);
    }
    
    // Check for negative amounts
    const negativeAmounts = db.prepare(`
      SELECT COUNT(*) as count FROM invoices 
      WHERE CAST(amount AS REAL) < 0
    `).get();
    
    if (negativeAmounts.count > 0) {
      healthReport.dataIntegrity.issues.push(`${negativeAmounts.count} invoices with negative amounts`);
    }
    
    db.close();
    healthReport.dataIntegrity.status = healthReport.dataIntegrity.issues.length === 0 ? 'healthy' : 'issues';
    
  } catch (error) {
    console.error('❌ Data integrity check failed:', error.message);
    healthReport.dataIntegrity.issues.push(`Data integrity check failed: ${error.message}`);
    healthReport.dataIntegrity.status = 'critical';
  }
}

// Runtime Error Analysis
async function runtimeErrorCheck() {
  console.log('\n🚨 RUNTIME ERROR ANALYSIS');
  console.log('=========================');
  
  try {
    // Check for common runtime errors by testing edge cases
    console.log('🔍 Testing edge cases...');
    
    // Test invalid product ID
    try {
      await axios.get(`${BASE_URL}/api/products/99999`, { timeout: 5000, validateStatus: () => true });
    } catch (error) {
      // This is expected
    }
    
    // Test invalid checkout page
    try {
      await axios.get(`${BASE_URL}/checkout/invalid-slug`, { timeout: 5000, validateStatus: () => true });
    } catch (error) {
      // This is expected
    }
    
    // Test malformed requests
    try {
      await axios.post(`${BASE_URL}/api/admin/login`, { invalid: 'data' }, { timeout: 5000, validateStatus: () => true });
    } catch (error) {
      // This is expected
    }
    
    healthReport.runtime.status = 'healthy';
    
  } catch (error) {
    console.error('❌ Runtime error check failed:', error.message);
    healthReport.runtime.issues.push(`Runtime error check failed: ${error.message}`);
    healthReport.runtime.status = 'critical';
  }
}

// Generate Overall Score
function generateOverallScore() {
  const components = ['database', 'api', 'frontend', 'functionality', 'dataIntegrity', 'runtime'];
  let totalScore = 0;
  let criticalIssues = 0;
  
  components.forEach(component => {
    const comp = healthReport[component];
    if (comp.status === 'healthy') {
      totalScore += Math.floor(100 / components.length);
    } else if (comp.status === 'issues') {
      totalScore += Math.floor(50 / components.length);
    }
    
    criticalIssues += comp.issues.length;
  });
  
  healthReport.overall.score = Math.min(totalScore, 100);
  healthReport.overall.criticalIssues = criticalIssues;
  
  if (criticalIssues === 0 && totalScore >= 90) {
    healthReport.overall.status = 'excellent';
  } else if (criticalIssues === 0 && totalScore >= 70) {
    healthReport.overall.status = 'healthy';
  } else if (criticalIssues <= 3) {
    healthReport.overall.status = 'warning';
  } else {
    healthReport.overall.status = 'critical';
  }
}

// Main execution
async function runLiveHealthCheck() {
  try {
    await liveDatabaseCheck();
    await liveAPICheck();
    await frontendCheck();
    await functionalityCheck();
    await dataIntegrityCheck();
    await runtimeErrorCheck();
    
    generateOverallScore();
    
    // Print comprehensive report
    console.log('\n📊 LIVE SYSTEM HEALTH CHECK RESULTS');
    console.log('====================================');
    
    const getStatusIcon = (status) => {
      switch (status) {
        case 'excellent': return '🌟';
        case 'healthy': return '✅';
        case 'warning': return '⚠️ ';
        case 'issues': return '🔶';
        case 'critical': return '🚨';
        default: return '❓';
      }
    };
    
    console.log(`${getStatusIcon(healthReport.database.status)} Database: ${healthReport.database.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.api.status)} API: ${healthReport.api.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.frontend.status)} Frontend: ${healthReport.frontend.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.functionality.status)} Functionality: ${healthReport.functionality.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.dataIntegrity.status)} Data Integrity: ${healthReport.dataIntegrity.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.runtime.status)} Runtime: ${healthReport.runtime.status.toUpperCase()}`);
    console.log(`${getStatusIcon(healthReport.overall.status)} Overall: ${healthReport.overall.status.toUpperCase()} (${healthReport.overall.score}%)`);
    
    // Print detailed findings
    const allIssues = [
      ...healthReport.database.issues,
      ...healthReport.api.issues,
      ...healthReport.frontend.issues,
      ...healthReport.functionality.issues,
      ...healthReport.dataIntegrity.issues,
      ...healthReport.runtime.issues
    ];
    
    const allWarnings = [
      ...healthReport.database.warnings,
      ...healthReport.api.warnings,
      ...healthReport.frontend.warnings,
      ...healthReport.functionality.warnings,
      ...healthReport.dataIntegrity.warnings,
      ...healthReport.runtime.warnings
    ];
    
    if (allIssues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES FOUND:');
      allIssues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
    }
    
    if (allWarnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      allWarnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning}`);
      });
    }
    
    // Print data summary
    console.log('\n📊 DATA SUMMARY:');
    Object.entries(healthReport.database.data).forEach(([table, data]) => {
      console.log(`   ${table}: ${data.count} records`);
    });
    
    // Print functionality summary
    console.log('\n🔧 FUNCTIONALITY SUMMARY:');
    Object.entries(healthReport.functionality.features).forEach(([feature, data]) => {
      const status = data.working ? '✅' : '❌';
      console.log(`   ${status} ${feature}: ${data.working ? 'Working' : 'Failed'}`);
    });
    
    // Save detailed report
    fs.writeFileSync('live-health-report.json', JSON.stringify(healthReport, null, 2));
    console.log('\n📄 Detailed report saved to: live-health-report.json');
    
    if (allIssues.length === 0) {
      console.log('\n🎉 NO CRITICAL ISSUES FOUND!');
      console.log('🌟 SYSTEM IS RUNNING SMOOTHLY WITH REAL DATA!');
    } else {
      console.log(`\n⚠️  ${allIssues.length} CRITICAL ISSUES NEED ATTENTION`);
    }
    
  } catch (error) {
    console.error('🚨 Live health check failed:', error);
    process.exit(1);
  }
}

// Run the live health check
runLiveHealthCheck();
