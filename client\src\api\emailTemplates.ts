import { apiRequest } from '@/lib/queryClient';
import { EmailTemplate, EmailTemplateCategory } from '@/lib/email-templates';

// API functions
export const getEmailTemplates = async (): Promise<EmailTemplate[]> => {
  return apiRequest('/api/email-templates', 'GET');
};

export const getEmailTemplate = async (id: number): Promise<EmailTemplate> => {
  return apiRequest(`/api/email-templates/${id}`, 'GET');
};

export const getEmailTemplatesByCategory = async (category: string): Promise<EmailTemplate[]> => {
  return apiRequest(`/api/email-templates/category/${category}`, 'GET');
};

export const getEmailTemplateCategories = async (): Promise<EmailTemplateCategory[]> => {
  return apiRequest('/api/email-templates/categories', 'GET');
};

export const createEmailTemplate = async (data: Partial<EmailTemplate>): Promise<EmailTemplate> => {
  return apiRequest('/api/email-templates', 'POST', data);
};

export const updateEmailTemplate = async (id: number, data: Partial<EmailTemplate>): Promise<EmailTemplate> => {
  return apiRequest(`/api/email-templates/${id}`, 'PUT', data);
};

export const deleteEmailTemplate = async (id: number): Promise<{ message: string }> => {
  return apiRequest(`/api/email-templates/${id}`, 'DELETE');
};

export const clearAllEmailTemplates = async (): Promise<{ message: string; deletedCount: number; totalTemplates: number }> => {
  return apiRequest('/api/email-templates/clear/all', 'DELETE');
};

export const forceClearAllEmailTemplates = async (): Promise<{ message: string; deletedCount: number; totalTemplates: number }> => {
  return apiRequest('/api/email-templates/force-clear/all', 'DELETE');
};

export const previewEmailTemplate = async (templateId: number, testData: Record<string, string>): Promise<{
  subject: string;
  htmlContent: string;
  textContent: string;
}> => {
  return apiRequest('/api/email-templates/preview', 'POST', { templateId, testData });
};
