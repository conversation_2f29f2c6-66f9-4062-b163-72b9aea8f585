const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

console.log('🔧 FIXING ALL DATABASE ISSUES');
console.log('==============================\n');

// Initialize database
const dbPath = './data.db';
let db;

try {
  db = new Database(dbPath);
  console.log('✅ Connected to database:', dbPath);
} catch (error) {
  console.error('❌ Failed to connect to database:', error);
  process.exit(1);
}

// Create all required tables
async function createAllTables() {
  console.log('\n📊 CREATING DATABASE SCHEMA');
  console.log('============================');

  const tables = [
    {
      name: 'users',
      sql: `
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY,
          username TEXT NOT NULL UNIQUE,
          password TEXT NOT NULL
        )
      `
    },
    {
      name: 'products',
      sql: `
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY,
          name TEXT NOT NULL,
          description TEXT NOT NULL,
          price TEXT NOT NULL,
          image_url TEXT NOT NULL,
          active INTEGER NOT NULL DEFAULT 1
        )
      `
    },
    {
      name: 'invoices',
      sql: `
        CREATE TABLE IF NOT EXISTS invoices (
          id INTEGER PRIMARY KEY,
          customer_name TEXT NOT NULL,
          customer_email TEXT NOT NULL,
          product_id INTEGER NOT NULL,
          amount TEXT NOT NULL,
          status TEXT NOT NULL DEFAULT 'pending',
          paypal_invoice_id TEXT,
          paypal_invoice_url TEXT,
          is_trial_order INTEGER NOT NULL DEFAULT 0,
          has_upgraded INTEGER NOT NULL DEFAULT 0,
          upgraded_at TEXT,
          created_at TEXT NOT NULL,
          custom_checkout_page_id INTEGER,
          country TEXT,
          app_type TEXT,
          mac_address TEXT
        )
      `
    },
    {
      name: 'custom_checkout_pages',
      sql: `
        CREATE TABLE IF NOT EXISTS custom_checkout_pages (
          id INTEGER PRIMARY KEY,
          slug TEXT NOT NULL UNIQUE,
          title TEXT NOT NULL,
          description TEXT,
          product_id INTEGER NOT NULL,
          custom_price TEXT,
          custom_currency TEXT DEFAULT 'USD',
          background_color TEXT DEFAULT '#ffffff',
          text_color TEXT DEFAULT '#000000',
          button_color TEXT DEFAULT '#007bff',
          logo_url TEXT,
          custom_css TEXT,
          active INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL,
          seo_title TEXT,
          seo_description TEXT,
          seo_keywords TEXT,
          expires_at TEXT,
          view_count INTEGER DEFAULT 0,
          conversion_count INTEGER DEFAULT 0,
          confirmation_message TEXT,
          header_logo TEXT,
          footer_logo TEXT,
          theme_mode TEXT NOT NULL DEFAULT 'light',
          embed_code_id TEXT
        )
      `
    },
    {
      name: 'allowed_emails',
      sql: `
        CREATE TABLE IF NOT EXISTS allowed_emails (
          id INTEGER PRIMARY KEY,
          domain TEXT NOT NULL UNIQUE,
          created_at TEXT NOT NULL
        )
      `
    },
    {
      name: 'smtp_providers',
      sql: `
        CREATE TABLE IF NOT EXISTS smtp_providers (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          host TEXT NOT NULL,
          port TEXT NOT NULL,
          secure INTEGER NOT NULL DEFAULT 0,
          username TEXT NOT NULL,
          password TEXT NOT NULL,
          from_email TEXT NOT NULL,
          from_name TEXT NOT NULL,
          active INTEGER NOT NULL DEFAULT 1,
          is_default INTEGER NOT NULL DEFAULT 0,
          is_backup INTEGER NOT NULL DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      `
    },
    {
      name: 'email_templates',
      sql: `
        CREATE TABLE IF NOT EXISTS email_templates (
          id INTEGER PRIMARY KEY,
          template_id TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          description TEXT,
          subject TEXT NOT NULL,
          html_content TEXT NOT NULL,
          text_content TEXT,
          content TEXT,
          category TEXT NOT NULL DEFAULT 'general',
          is_default INTEGER NOT NULL DEFAULT 0,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      `
    },
    {
      name: 'paypal_buttons',
      sql: `
        CREATE TABLE IF NOT EXISTS paypal_buttons (
          id INTEGER PRIMARY KEY,
          button_id TEXT NOT NULL UNIQUE,
          name TEXT NOT NULL,
          description TEXT,
          product_id INTEGER NOT NULL,
          amount TEXT NOT NULL,
          currency TEXT NOT NULL DEFAULT 'USD',
          button_code TEXT NOT NULL,
          active INTEGER NOT NULL DEFAULT 1,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      `
    },
    {
      name: 'custom_invoices',
      sql: `
        CREATE TABLE IF NOT EXISTS custom_invoices (
          id INTEGER PRIMARY KEY,
          invoice_number TEXT NOT NULL UNIQUE,
          customer_name TEXT NOT NULL,
          customer_email TEXT NOT NULL,
          amount TEXT NOT NULL,
          currency TEXT NOT NULL DEFAULT 'USD',
          description TEXT,
          status TEXT NOT NULL DEFAULT 'pending',
          due_date TEXT,
          created_at TEXT NOT NULL,
          updated_at TEXT NOT NULL
        )
      `
    }
  ];

  for (const table of tables) {
    try {
      db.exec(table.sql);
      console.log(`✅ Created table: ${table.name}`);
    } catch (error) {
      console.error(`❌ Failed to create table ${table.name}:`, error.message);
    }
  }
}

// Insert default data
async function insertDefaultData() {
  console.log('\n📝 INSERTING DEFAULT DATA');
  console.log('==========================');

  try {
    // Insert default admin user
    const existingUsers = db.prepare('SELECT COUNT(*) as count FROM users').get();
    if (existingUsers.count === 0) {
      db.prepare(`
        INSERT INTO users (username, password) 
        VALUES (?, ?)
      `).run('admin', 'admin123');
      console.log('✅ Created default admin user (admin/admin123)');
    } else {
      console.log('ℹ️  Admin user already exists');
    }

    // Insert default SMTP provider
    const existingSmtp = db.prepare('SELECT COUNT(*) as count FROM smtp_providers').get();
    if (existingSmtp.count === 0) {
      const now = new Date().toISOString();
      db.prepare(`
        INSERT INTO smtp_providers (
          id, name, host, port, secure, username, password, 
          from_email, from_name, active, is_default, is_backup, 
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        'smtp-1',
        'Primary SMTP',
        'smtp-relay.brevo.com',
        '587',
        0,
        '<EMAIL>',
        '3d8I9xFm1yMDYj7W',
        '<EMAIL>',
        'PayPal Invoicer',
        1,
        1,
        0,
        now,
        now
      );
      console.log('✅ Created default SMTP provider');
    } else {
      console.log('ℹ️  SMTP provider already exists');
    }

    // Insert default products
    const existingProducts = db.prepare('SELECT COUNT(*) as count FROM products').get();
    if (existingProducts.count === 0) {
      const products = [
        {
          name: 'Dashboard Pro Template',
          description: 'Professional dashboard template with modern design',
          price: '29.99',
          image_url: '/api/placeholder/400/300'
        },
        {
          name: 'E-commerce Starter Kit',
          description: 'Complete e-commerce solution with payment integration',
          price: '49.99',
          image_url: '/api/placeholder/400/300'
        },
        {
          name: 'Mobile App UI Kit',
          description: 'Comprehensive mobile app UI components',
          price: '39.99',
          image_url: '/api/placeholder/400/300'
        }
      ];

      const insertProduct = db.prepare(`
        INSERT INTO products (name, description, price, image_url, active)
        VALUES (?, ?, ?, ?, 1)
      `);

      for (const product of products) {
        insertProduct.run(product.name, product.description, product.price, product.image_url);
      }
      console.log(`✅ Created ${products.length} default products`);
    } else {
      console.log('ℹ️  Products already exist');
    }

  } catch (error) {
    console.error('❌ Error inserting default data:', error);
  }
}

// Verify database integrity
async function verifyDatabase() {
  console.log('\n🔍 VERIFYING DATABASE INTEGRITY');
  console.log('================================');

  const expectedTables = [
    'users', 'products', 'invoices', 'custom_checkout_pages',
    'allowed_emails', 'smtp_providers', 'email_templates',
    'paypal_buttons', 'custom_invoices'
  ];

  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  const existingTableNames = tables.map(t => t.name);

  let allTablesExist = true;
  for (const expectedTable of expectedTables) {
    if (existingTableNames.includes(expectedTable)) {
      const count = db.prepare(`SELECT COUNT(*) as count FROM ${expectedTable}`).get();
      console.log(`✅ ${expectedTable}: ${count.count} records`);
    } else {
      console.log(`❌ ${expectedTable}: MISSING`);
      allTablesExist = false;
    }
  }

  return allTablesExist;
}

// Main execution
async function fixDatabase() {
  try {
    await createAllTables();
    await insertDefaultData();
    const isValid = await verifyDatabase();

    console.log('\n📊 DATABASE FIX SUMMARY');
    console.log('========================');
    
    if (isValid) {
      console.log('✅ Database schema is now complete and valid!');
      console.log('✅ All required tables have been created');
      console.log('✅ Default data has been inserted');
      console.log('\n🎉 Database issues have been resolved!');
    } else {
      console.log('❌ Some issues remain - please check the logs above');
    }

  } catch (error) {
    console.error('🚨 Critical error during database fix:', error);
  } finally {
    if (db) {
      db.close();
      console.log('\n📝 Database connection closed');
    }
  }
}

// Run the fix
fixDatabase();
