const axios = require('axios');
const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001';
const ADMIN_TOKEN = 's3cur3-4dm1n-4cc3ss-t0k3n';

console.log('🔍 COMPREHENSIVE ERROR DETECTION & ANALYSIS');
console.log('============================================\n');

let errorReport = {
  timestamp: new Date().toISOString(),
  serverErrors: [],
  apiErrors: [],
  databaseErrors: [],
  fileSystemErrors: [],
  configurationErrors: [],
  runtimeErrors: [],
  performanceIssues: [],
  securityIssues: [],
  dataConsistencyErrors: [],
  integrationErrors: [],
  summary: {
    totalErrors: 0,
    criticalErrors: 0,
    warnings: 0,
    status: 'unknown'
  }
};

// Test all API endpoints comprehensively
async function testAllAPIEndpoints() {
  console.log('🌐 TESTING ALL API ENDPOINTS');
  console.log('============================');
  
  let adminSession = null;
  
  try {
    // Get admin session
    const loginResponse = await axios.post(`${BASE_URL}/api/admin/login?token=${ADMIN_TOKEN}`, {
      username: 'admin',
      password: 'admin123'
    });
    
    const cookies = loginResponse.headers['set-cookie'];
    if (cookies) {
      adminSession = cookies.find(cookie => cookie.startsWith('connect.sid'));
    }
    
    const authConfig = adminSession ? { headers: { Cookie: adminSession } } : {};
    
    // Comprehensive list of all endpoints
    const endpoints = [
      // Public endpoints
      { method: 'GET', path: '/api/health', name: 'Health Check', public: true },
      { method: 'GET', path: '/api/products', name: 'Products List', public: true },
      { method: 'GET', path: '/api/system-messages', name: 'System Messages', public: true },
      { method: 'GET', path: '/api/homepage', name: 'Homepage Config', public: true },
      
      // Admin endpoints
      { method: 'GET', path: '/api/admin/stats', name: 'Admin Stats', admin: true },
      { method: 'GET', path: '/api/admin/products', name: 'Admin Products', admin: true },
      { method: 'GET', path: '/api/admin/invoices', name: 'Admin Invoices', admin: true },
      { method: 'GET', path: '/api/admin/email-config', name: 'Email Config', admin: true },
      { method: 'GET', path: '/api/admin/payment-config', name: 'Payment Config', admin: true },
      { method: 'GET', path: '/api/admin/general-settings', name: 'General Settings', admin: true },
      { method: 'GET', path: '/api/admin/backup-list', name: 'Backup List', admin: true },
      { method: 'GET', path: '/api/admin/check-session', name: 'Session Check', admin: true },
      
      // Protected endpoints
      { method: 'GET', path: '/api/smtp-providers', name: 'SMTP Providers', protected: true },
      { method: 'GET', path: '/api/email-templates', name: 'Email Templates', protected: true },
      { method: 'GET', path: '/api/custom-checkout-pages', name: 'Custom Checkout', protected: true },
      { method: 'GET', path: '/api/invoices', name: 'Invoices', protected: true },
      { method: 'GET', path: '/api/paypal-buttons', name: 'PayPal Buttons', protected: true },
      { method: 'GET', path: '/api/general-settings', name: 'General Settings Protected', protected: true },
      { method: 'GET', path: '/api/allowed-emails', name: 'Allowed Emails', protected: true },
      { method: 'GET', path: '/api/custom-invoices', name: 'Custom Invoices', protected: true },
      
      // Additional endpoints that might exist
      { method: 'GET', path: '/api/admin/system/backups', name: 'System Backups', admin: true },
      { method: 'GET', path: '/api/admin/data/products', name: 'Data Export Products', admin: true },
      { method: 'GET', path: '/api/admin/data/orders', name: 'Data Export Orders', admin: true },
      { method: 'GET', path: '/api/admin/data/settings', name: 'Data Export Settings', admin: true },
      { method: 'GET', path: '/api/telegram/config', name: 'Telegram Config', protected: true },
      { method: 'GET', path: '/api/upload/images', name: 'Upload Images', protected: true },
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`🔍 Testing: ${endpoint.name}`);
        
        let config = { timeout: 10000, validateStatus: () => true };
        let url = `${BASE_URL}${endpoint.path}`;
        
        if (endpoint.admin) {
          url += `?token=${ADMIN_TOKEN}`;
          if (adminSession) {
            config.headers = { Cookie: adminSession };
          }
        } else if (endpoint.protected) {
          if (adminSession) {
            config.headers = { Cookie: adminSession };
          }
        }
        
        const response = await axios({
          method: endpoint.method,
          url: url,
          ...config
        });
        
        // Analyze response for errors
        if (response.status >= 500) {
          errorReport.apiErrors.push({
            endpoint: endpoint.name,
            path: endpoint.path,
            status: response.status,
            error: response.data?.message || 'Server error',
            severity: 'critical'
          });
          console.log(`   ❌ ${endpoint.name}: Server Error (${response.status})`);
        } else if (response.status === 404 && !endpoint.path.includes('invalid')) {
          errorReport.apiErrors.push({
            endpoint: endpoint.name,
            path: endpoint.path,
            status: response.status,
            error: 'Endpoint not found',
            severity: 'high'
          });
          console.log(`   ❌ ${endpoint.name}: Not Found (404)`);
        } else if (response.status >= 400 && endpoint.public) {
          errorReport.apiErrors.push({
            endpoint: endpoint.name,
            path: endpoint.path,
            status: response.status,
            error: response.data?.message || 'Client error',
            severity: 'medium'
          });
          console.log(`   ⚠️  ${endpoint.name}: Client Error (${response.status})`);
        } else {
          console.log(`   ✅ ${endpoint.name}: OK (${response.status})`);
          
          // Check for data quality issues
          if (response.data && typeof response.data === 'object') {
            if (response.data.error) {
              errorReport.apiErrors.push({
                endpoint: endpoint.name,
                path: endpoint.path,
                status: response.status,
                error: `Response contains error: ${response.data.error}`,
                severity: 'medium'
              });
            }
          }
        }
        
      } catch (error) {
        errorReport.apiErrors.push({
          endpoint: endpoint.name,
          path: endpoint.path,
          status: 'NETWORK_ERROR',
          error: error.message,
          severity: 'critical'
        });
        console.log(`   ❌ ${endpoint.name}: ${error.message}`);
      }
    }
    
  } catch (error) {
    errorReport.apiErrors.push({
      endpoint: 'Authentication',
      path: '/api/admin/login',
      status: 'AUTH_ERROR',
      error: error.message,
      severity: 'critical'
    });
  }
}

// Check database for errors and inconsistencies
async function checkDatabaseErrors() {
  console.log('\n🗄️  DATABASE ERROR ANALYSIS');
  console.log('============================');
  
  try {
    const db = new Database('./data.db');
    
    // Check database integrity
    const integrityCheck = db.prepare('PRAGMA integrity_check').get();
    if (integrityCheck.integrity_check !== 'ok') {
      errorReport.databaseErrors.push({
        type: 'integrity',
        error: `Database integrity check failed: ${integrityCheck.integrity_check}`,
        severity: 'critical'
      });
    }
    
    // Check for orphaned records
    console.log('🔍 Checking for orphaned records...');
    
    // Orphaned invoices
    const orphanedInvoices = db.prepare(`
      SELECT COUNT(*) as count FROM invoices 
      WHERE product_id NOT IN (SELECT id FROM products)
    `).get();
    
    if (orphanedInvoices.count > 0) {
      errorReport.databaseErrors.push({
        type: 'orphaned_data',
        error: `${orphanedInvoices.count} invoices reference non-existent products`,
        severity: 'high'
      });
    }
    
    // Orphaned checkout pages
    const orphanedPages = db.prepare(`
      SELECT COUNT(*) as count FROM custom_checkout_pages 
      WHERE product_id NOT IN (SELECT id FROM products)
    `).get();
    
    if (orphanedPages.count > 0) {
      errorReport.databaseErrors.push({
        type: 'orphaned_data',
        error: `${orphanedPages.count} checkout pages reference non-existent products`,
        severity: 'high'
      });
    }
    
    // Check for invalid data
    console.log('🔍 Checking for invalid data...');
    
    // Invalid email addresses
    const invalidEmails = db.prepare(`
      SELECT COUNT(*) as count FROM invoices 
      WHERE customer_email NOT LIKE '%@%' OR customer_email NOT LIKE '%.%'
    `).get();
    
    if (invalidEmails.count > 0) {
      errorReport.databaseErrors.push({
        type: 'invalid_data',
        error: `${invalidEmails.count} invoices have invalid email addresses`,
        severity: 'medium'
      });
    }
    
    // Negative amounts
    const negativeAmounts = db.prepare(`
      SELECT COUNT(*) as count FROM invoices 
      WHERE CAST(amount AS REAL) < 0
    `).get();
    
    if (negativeAmounts.count > 0) {
      errorReport.databaseErrors.push({
        type: 'invalid_data',
        error: `${negativeAmounts.count} invoices have negative amounts`,
        severity: 'high'
      });
    }
    
    // Check for duplicate slugs
    const duplicateSlugs = db.prepare(`
      SELECT slug, COUNT(*) as count 
      FROM custom_checkout_pages 
      GROUP BY slug 
      HAVING COUNT(*) > 1
    `).all();
    
    if (duplicateSlugs.length > 0) {
      errorReport.databaseErrors.push({
        type: 'duplicate_data',
        error: `${duplicateSlugs.length} duplicate checkout page slugs found`,
        severity: 'high'
      });
    }
    
    // Check for missing required data
    const adminUsers = db.prepare('SELECT COUNT(*) as count FROM users WHERE username = ?').get('admin');
    if (adminUsers.count === 0) {
      errorReport.databaseErrors.push({
        type: 'missing_data',
        error: 'No admin user found in database',
        severity: 'critical'
      });
    }
    
    db.close();
    console.log('✅ Database error check completed');
    
  } catch (error) {
    errorReport.databaseErrors.push({
      type: 'connection',
      error: `Database connection failed: ${error.message}`,
      severity: 'critical'
    });
  }
}

// Check file system errors
async function checkFileSystemErrors() {
  console.log('\n📁 FILE SYSTEM ERROR ANALYSIS');
  console.log('==============================');
  
  try {
    // Check required directories
    const requiredDirs = ['uploads', 'server/backups'];
    
    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        errorReport.fileSystemErrors.push({
          type: 'missing_directory',
          error: `Required directory missing: ${dir}`,
          severity: 'medium'
        });
        console.log(`⚠️  Missing directory: ${dir}`);
      } else {
        console.log(`✅ Directory exists: ${dir}`);
      }
    }
    
    // Check file permissions
    try {
      fs.accessSync('./data.db', fs.constants.R_OK | fs.constants.W_OK);
      console.log('✅ Database file permissions OK');
    } catch (error) {
      errorReport.fileSystemErrors.push({
        type: 'permissions',
        error: `Database file permission error: ${error.message}`,
        severity: 'critical'
      });
    }
    
    // Check for large files that might cause issues
    if (fs.existsSync('./data.db')) {
      const dbStats = fs.statSync('./data.db');
      if (dbStats.size > 100 * 1024 * 1024) { // 100MB
        errorReport.performanceIssues.push({
          type: 'large_database',
          error: `Database file is large: ${(dbStats.size / 1024 / 1024).toFixed(2)}MB`,
          severity: 'medium'
        });
      }
    }
    
  } catch (error) {
    errorReport.fileSystemErrors.push({
      type: 'general',
      error: `File system check failed: ${error.message}`,
      severity: 'high'
    });
  }
}

// Check configuration errors
async function checkConfigurationErrors() {
  console.log('\n⚙️  CONFIGURATION ERROR ANALYSIS');
  console.log('=================================');
  
  try {
    // Check environment variables
    const requiredEnvVars = ['NODE_ENV', 'SESSION_SECRET', 'ADMIN_ACCESS_TOKEN'];
    
    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        errorReport.configurationErrors.push({
          type: 'missing_env_var',
          error: `Missing environment variable: ${envVar}`,
          severity: 'high'
        });
        console.log(`⚠️  Missing env var: ${envVar}`);
      } else {
        console.log(`✅ Env var exists: ${envVar}`);
      }
    }
    
    // Check weak configurations
    if (process.env.SESSION_SECRET === 'local-development-secret') {
      errorReport.securityIssues.push({
        type: 'weak_secret',
        error: 'Using default session secret',
        severity: 'high'
      });
    }
    
    if (process.env.ADMIN_ACCESS_TOKEN && process.env.ADMIN_ACCESS_TOKEN.length < 20) {
      errorReport.securityIssues.push({
        type: 'weak_token',
        error: 'Admin access token is too short',
        severity: 'medium'
      });
    }
    
  } catch (error) {
    errorReport.configurationErrors.push({
      type: 'general',
      error: `Configuration check failed: ${error.message}`,
      severity: 'high'
    });
  }
}

// Test edge cases and error handling
async function testErrorHandling() {
  console.log('\n🧪 ERROR HANDLING TESTS');
  console.log('========================');
  
  try {
    // Test invalid requests
    const errorTests = [
      { method: 'GET', path: '/api/products/99999', name: 'Invalid Product ID' },
      { method: 'GET', path: '/checkout/invalid-slug', name: 'Invalid Checkout Slug' },
      { method: 'POST', path: '/api/admin/login', data: { invalid: 'data' }, name: 'Invalid Login Data' },
      { method: 'GET', path: '/api/nonexistent', name: 'Nonexistent Endpoint' },
      { method: 'POST', path: '/api/products', data: {}, name: 'Invalid Product Creation' },
    ];
    
    for (const test of errorTests) {
      try {
        const config = {
          method: test.method,
          url: `${BASE_URL}${test.path}`,
          timeout: 5000,
          validateStatus: () => true
        };
        
        if (test.data) {
          config.data = test.data;
        }
        
        const response = await axios(config);
        
        // Check if error handling is appropriate
        if (response.status >= 500) {
          errorReport.runtimeErrors.push({
            test: test.name,
            error: `Server error on invalid request: ${response.status}`,
            severity: 'medium'
          });
          console.log(`⚠️  ${test.name}: Server error (${response.status})`);
        } else {
          console.log(`✅ ${test.name}: Handled correctly (${response.status})`);
        }
        
      } catch (error) {
        if (error.code === 'ECONNREFUSED') {
          errorReport.runtimeErrors.push({
            test: test.name,
            error: 'Server connection refused',
            severity: 'critical'
          });
        }
      }
    }
    
  } catch (error) {
    errorReport.runtimeErrors.push({
      test: 'Error Handling Tests',
      error: `Error handling test failed: ${error.message}`,
      severity: 'high'
    });
  }
}

// Generate summary
function generateSummary() {
  const allErrors = [
    ...errorReport.serverErrors,
    ...errorReport.apiErrors,
    ...errorReport.databaseErrors,
    ...errorReport.fileSystemErrors,
    ...errorReport.configurationErrors,
    ...errorReport.runtimeErrors,
    ...errorReport.performanceIssues,
    ...errorReport.securityIssues,
    ...errorReport.dataConsistencyErrors,
    ...errorReport.integrationErrors
  ];
  
  const criticalErrors = allErrors.filter(e => e.severity === 'critical').length;
  const highErrors = allErrors.filter(e => e.severity === 'high').length;
  const mediumErrors = allErrors.filter(e => e.severity === 'medium').length;
  
  errorReport.summary = {
    totalErrors: allErrors.length,
    criticalErrors: criticalErrors,
    highErrors: highErrors,
    mediumErrors: mediumErrors,
    warnings: mediumErrors,
    status: criticalErrors > 0 ? 'critical' : 
             highErrors > 0 ? 'warning' : 
             mediumErrors > 0 ? 'minor_issues' : 'healthy'
  };
}

// Main execution
async function runComprehensiveErrorDetection() {
  try {
    await testAllAPIEndpoints();
    await checkDatabaseErrors();
    await checkFileSystemErrors();
    await checkConfigurationErrors();
    await testErrorHandling();
    
    generateSummary();
    
    // Print comprehensive report
    console.log('\n📊 COMPREHENSIVE ERROR DETECTION RESULTS');
    console.log('=========================================');
    
    const getStatusIcon = (status) => {
      switch (status) {
        case 'healthy': return '✅';
        case 'minor_issues': return '⚠️ ';
        case 'warning': return '🔶';
        case 'critical': return '🚨';
        default: return '❓';
      }
    };
    
    console.log(`${getStatusIcon(errorReport.summary.status)} Overall Status: ${errorReport.summary.status.toUpperCase()}`);
    console.log(`🚨 Critical Errors: ${errorReport.summary.criticalErrors}`);
    console.log(`🔶 High Priority Errors: ${errorReport.summary.highErrors}`);
    console.log(`⚠️  Medium Priority Issues: ${errorReport.summary.mediumErrors}`);
    console.log(`📊 Total Issues Found: ${errorReport.summary.totalErrors}`);
    
    // Print all errors by category
    const categories = [
      { name: 'API Errors', errors: errorReport.apiErrors },
      { name: 'Database Errors', errors: errorReport.databaseErrors },
      { name: 'File System Errors', errors: errorReport.fileSystemErrors },
      { name: 'Configuration Errors', errors: errorReport.configurationErrors },
      { name: 'Runtime Errors', errors: errorReport.runtimeErrors },
      { name: 'Performance Issues', errors: errorReport.performanceIssues },
      { name: 'Security Issues', errors: errorReport.securityIssues }
    ];
    
    categories.forEach(category => {
      if (category.errors.length > 0) {
        console.log(`\n🔍 ${category.name.toUpperCase()}:`);
        category.errors.forEach((error, index) => {
          const icon = error.severity === 'critical' ? '🚨' : 
                      error.severity === 'high' ? '🔶' : '⚠️ ';
          console.log(`${index + 1}. ${icon} ${error.error}`);
          if (error.endpoint) console.log(`   Endpoint: ${error.endpoint}`);
          if (error.path) console.log(`   Path: ${error.path}`);
        });
      }
    });
    
    // Save detailed report
    fs.writeFileSync('comprehensive-error-report.json', JSON.stringify(errorReport, null, 2));
    console.log('\n📄 Detailed error report saved to: comprehensive-error-report.json');
    
    if (errorReport.summary.totalErrors === 0) {
      console.log('\n🎉 NO ERRORS FOUND!');
      console.log('🌟 SYSTEM IS RUNNING PERFECTLY!');
    } else {
      console.log(`\n⚠️  ${errorReport.summary.totalErrors} ISSUES NEED ATTENTION`);
      if (errorReport.summary.criticalErrors > 0) {
        console.log('🚨 CRITICAL ISSUES REQUIRE IMMEDIATE ATTENTION!');
      }
    }
    
  } catch (error) {
    console.error('🚨 Comprehensive error detection failed:', error);
    process.exit(1);
  }
}

// Run the comprehensive error detection
runComprehensiveErrorDetection();
