import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

export function useAdminAuth() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Query to check authentication status
  const { data, error, refetch } = useQuery({
    queryKey: ['/api/admin/check-session'],
    queryFn: async () => {
      try {
        const adminToken = import.meta.env.VITE_ADMIN_ACCESS_TOKEN || 'admin_code';
        const response = await fetch(`/api/admin/check-session?token=${adminToken}`, {
          credentials: 'include'
        });
        const data = await response.json();
        return data;
      } catch (error) {
        console.error('Error checking auth status:', error);
        return { isAuthenticated: false };
      }
    },
    retry: false,
    refetchOnWindowFocus: false,
  });

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: async (credentials: { username: string; password: string; rememberMe: boolean }) => {
      const adminToken = import.meta.env.VITE_ADMIN_ACCESS_TOKEN || 'admin_code';
      // apiRequest already parses the JSON response
      return apiRequest(`/api/admin/login?token=${adminToken}`, 'POST', credentials);
    },
    onSuccess: (data) => {
      // Refetch to update auth state properly
      refetch();

      toast({
        title: "Login successful",
        description: "Welcome to the admin dashboard",
      });

      // Small delay to ensure state is updated before redirecting
      setTimeout(() => {
        setLocation('/admin/dashboard');
      }, 50);
    },
    onError: (error) => {
      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "Invalid credentials",
        variant: "destructive"
      });
    }
  });

  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: async () => {
      // apiRequest already parses the JSON response
      return apiRequest('/api/admin/logout', 'POST');
    },
    onSuccess: () => {
      // Refetch to update auth state properly
      refetch();

      toast({
        title: "Logout successful",
      });

      // Small delay to ensure state is updated before redirecting
      setTimeout(() => {
        setLocation('/admin/login');
      }, 50);
    },
    onError: (error) => {
      toast({
        title: "Logout failed",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive"
      });
    }
  });

  // Update authentication state when data changes
  useEffect(() => {
    if (data) {
      setIsAuthenticated(data.isAuthenticated);
      setIsLoading(false);
    }
  }, [data]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (isAuthenticated === false && !isLoading && window.location.pathname.startsWith('/admin') && window.location.pathname !== '/admin/login') {
      setLocation('/admin/login');
    }
  }, [isAuthenticated, isLoading, setLocation]);

  return {
    isAuthenticated,
    isLoading,
    login: (credentials: { username: string; password: string; rememberMe: boolean }) => loginMutation.mutate(credentials),
    logout: () => logoutMutation.mutate(),
    checkAuth: () => refetch(),
    isPending: loginMutation.isPending || logoutMutation.isPending,
    authState: data // Include the full auth state for 2FA
  };
}