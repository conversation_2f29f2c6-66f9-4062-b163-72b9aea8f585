import { Router, Request, Response } from 'express';
import { getGeneralConfig } from '../general-config';

const robotsRouter = Router();

// Serve robots.txt
robotsRouter.get('/', (req: Request, res: Response) => {
  try {
    const config = getGeneralConfig();
    const robotsTxt = config.seoPrivacy.customRobotsTxt;

    // Set proper content type for robots.txt
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    
    // Add cache headers to prevent caching of robots.txt
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Send the robots.txt content
    res.send(robotsTxt);
  } catch (error) {
    console.error('Error serving robots.txt:', error);
    
    // Fallback to a restrictive robots.txt
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.send(`User-agent: *
Disallow: /`);
  }
});

export { robotsRouter };
