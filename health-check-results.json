{"database": {"status": "healthy", "issues": []}, "api": {"status": "healthy", "endpoints": [{"name": "Homepage", "path": "/", "status": 200, "success": true, "critical": true, "responseTime": "N/A", "error": null}, {"name": "Products API", "path": "/api/products", "status": 200, "success": true, "critical": true, "responseTime": "N/A", "error": null}, {"name": "Health Check", "path": "/api/health", "status": 200, "success": true, "critical": false, "responseTime": "N/A", "error": null}, {"name": "<PERSON><PERSON>", "path": "/admin/login", "status": 200, "success": true, "critical": true, "responseTime": "N/A", "error": null}, {"name": "General Settings", "path": "/api/general-settings", "status": 401, "success": true, "critical": false, "responseTime": "N/A", "error": null, "note": "Expected 401 - requires authentication"}, {"name": "SMTP Providers", "path": "/api/smtp-providers", "status": 401, "success": true, "critical": false, "responseTime": "N/A", "error": null, "note": "Expected 401 - requires authentication"}, {"name": "Email Templates", "path": "/api/email-templates", "status": 401, "success": true, "critical": false, "responseTime": "N/A", "error": null, "note": "Expected 401 - requires authentication"}, {"name": "Custom Checkout Pages", "path": "/api/custom-checkout-pages", "status": 401, "success": true, "critical": false, "responseTime": "N/A", "error": null, "note": "Expected 401 - requires authentication"}, {"name": "Invoices API", "path": "/api/invoices", "status": 401, "success": true, "critical": true, "responseTime": "N/A", "error": null, "note": "Expected 401 - requires authentication"}, {"name": "PayPal Buttons", "path": "/api/paypal-buttons", "status": 401, "success": true, "critical": false, "responseTime": "N/A", "error": null, "note": "Expected 401 - requires authentication"}], "issues": []}, "frontend": {"status": "healthy", "issues": []}, "features": {"status": "unknown", "working": [], "broken": []}, "overall": {"status": "healthy", "score": 100}}