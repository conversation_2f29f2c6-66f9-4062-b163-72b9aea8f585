// Test script to add and edit trial payment links
import fetch from 'node-fetch';

async function testTrialLinkFunctionality() {
  const baseUrl = 'http://localhost:3001';

  try {
    console.log('🧪 Testing trial payment link functionality...\n');

    // Step 1: Add a new trial payment link
    console.log('📝 Step 1: Adding a new trial payment link...');
    const testData = {
      name: 'Test Trial Link',
      paymentLink: 'https://example.com/test-trial-payment',
      buttonText: 'Start Test Trial',
      successRedirectUrl: 'https://example.com/test-success',
      active: true
    };

    const addResponse = await fetch(`${baseUrl}/api/direct-trial-link`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'connect.sid=s%3AkHSJRBquGPrw8SnJzMhmnfq0-j07hTKM'
      },
      body: JSON.stringify(testData)
    });

    console.log('Add Response status:', addResponse.status);
    const addResult = await addResponse.text();
    console.log('Add Response body:', addResult);

    if (addResponse.status === 200) {
      console.log('✅ Successfully added trial payment link!\n');

      // Step 2: Get the payment config to find the link ID
      console.log('📋 Step 2: Getting payment config to find the link ID...');
      const configResponse = await fetch(`${baseUrl}/api/admin/payment-config`, {
        method: 'GET',
        headers: {
          'Cookie': 'connect.sid=s%3AkHSJRBquGPrw8SnJzMhmnfq0-j07hTKM'
        }
      });

      if (configResponse.status === 200) {
        const config = await configResponse.json();
        const trialProvider = config.providers.find(p => p.id === 'trial-custom-link');

        if (trialProvider && trialProvider.config.links && trialProvider.config.links.length > 0) {
          const lastLink = trialProvider.config.links[trialProvider.config.links.length - 1];
          console.log('Found trial link:', lastLink);

          // Step 3: Test editing the trial payment link
          console.log('\n✏️ Step 3: Testing edit functionality...');
          const editData = {
            name: 'Updated Test Trial Link',
            paymentLink: 'https://example.com/updated-trial-payment',
            buttonText: 'Start Updated Trial',
            successRedirectUrl: 'https://example.com/updated-success',
            active: true
          };

          const editResponse = await fetch(`${baseUrl}/api/admin/payment-config/trial-custom-link/${lastLink.id}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': 'connect.sid=s%3AkHSJRBquGPrw8SnJzMhmnfq0-j07hTKM'
            },
            body: JSON.stringify(editData)
          });

          console.log('Edit Response status:', editResponse.status);
          const editResult = await editResponse.text();
          console.log('Edit Response body:', editResult);

          if (editResponse.status === 200) {
            console.log('✅ Successfully edited trial payment link!');
          } else {
            console.log('❌ Failed to edit trial payment link');
          }
        } else {
          console.log('❌ No trial links found in config');
        }
      } else {
        console.log('❌ Failed to get payment config');
      }
    } else if (addResponse.status === 401) {
      console.log('❌ Authentication failed - session might be expired');
    } else if (addResponse.status === 404) {
      console.log('❌ Endpoint not found');
    } else {
      console.log('🤔 Unexpected response');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

testTrialLinkFunctionality();
