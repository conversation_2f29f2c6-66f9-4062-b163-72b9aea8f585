import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { DEFAULT_SYSTEM_MESSAGES, SystemMessage } from '../../shared/system-messages';
import { systemMessages } from '../../shared/schema';
import { eq } from 'drizzle-orm';

export const systemMessagesRouter = Router();

// Simple admin check middleware
const checkAdmin = (req: Request, res: Response, next: Function) => {
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Define the validation schema for system messages (full schema)
const systemMessageSchema = z.object({
  messageId: z.string().min(1, "Message ID is required"),
  category: z.string().min(1, "Category is required"),
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  content: z.string().min(1, "Content is required"),
  isHtml: z.boolean().default(false)
});

// Define the validation schema for updating system messages (partial schema)
const updateSystemMessageSchema = z.object({
  content: z.string().min(1, "Content is required"),
  isHtml: z.boolean().default(false),
  // Optional fields that can be updated
  name: z.string().optional(),
  description: z.string().optional(),
  category: z.string().optional()
});

// In-memory storage for system messages (for demo purposes)
let systemMessagesStorage: SystemMessage[] = [...DEFAULT_SYSTEM_MESSAGES];

// Initialize system messages - we'll use in-memory storage for now
// In a production environment, this would be stored in a database
const initializeSystemMessages = () => {
  try {
    console.log('Using in-memory system messages storage');
    // We're already using the default messages from the shared file
  } catch (error) {
    console.error('Error initializing system messages:', error);
  }
};

// Call initialization function
initializeSystemMessages();

// Get all system messages
systemMessagesRouter.get('/', async (req: Request, res: Response) => {
  try {
    // Return the in-memory messages
    res.json(systemMessagesStorage);
  } catch (error) {
    console.error('Error fetching system messages:', error);
    res.status(500).json({ message: 'Failed to fetch system messages' });
  }
});

// Get a specific system message by ID
systemMessagesRouter.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const message = systemMessagesStorage.find(msg => msg.id === id);

    if (!message) {
      return res.status(404).json({ message: 'System message not found' });
    }

    res.json(message);
  } catch (error) {
    console.error('Error fetching system message:', error);
    res.status(500).json({ message: 'Failed to fetch system message' });
  }
});

// Update a system message
systemMessagesRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    console.log(`Updating system message ${id} with data:`, req.body);

    // Validate the request body using the update schema
    const validatedData = updateSystemMessageSchema.parse(req.body);

    // Check if the message exists
    const index = systemMessagesStorage.findIndex(msg => msg.id === id);

    if (index === -1) {
      return res.status(404).json({ message: 'System message not found' });
    }

    // Get the existing message
    const existingMessage = systemMessagesStorage[index];

    // Update only the provided fields, keeping existing values for others
    systemMessagesStorage[index] = {
      ...existingMessage,
      content: validatedData.content,
      isHtml: validatedData.isHtml,
      // Update optional fields only if provided
      ...(validatedData.name && { name: validatedData.name }),
      ...(validatedData.description !== undefined && { description: validatedData.description }),
      ...(validatedData.category && { category: validatedData.category })
    };

    console.log(`System message ${id} updated successfully`);
    res.json(systemMessagesStorage[index]);
  } catch (error) {
    console.error('Error updating system message:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({ message: 'Failed to update system message' });
  }
});

// Get system message by ID (for internal use)
export const getSystemMessage = (id: string): SystemMessage | undefined => {
  return systemMessagesStorage.find(msg => msg.id === id);
};

// Get all system messages by category (for internal use)
export const getSystemMessagesByCategory = (category: string): SystemMessage[] => {
  return systemMessagesStorage.filter(msg => msg.category === category);
};
