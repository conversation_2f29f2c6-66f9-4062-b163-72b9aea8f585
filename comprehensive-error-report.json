{"timestamp": "2025-06-03T14:12:08.583Z", "serverErrors": [], "apiErrors": [{"endpoint": "Backup List", "path": "/api/admin/backup-list", "status": 500, "error": "Failed to fetch backup list", "severity": "critical"}], "databaseErrors": [], "fileSystemErrors": [], "configurationErrors": [{"type": "missing_env_var", "error": "Missing environment variable: NODE_ENV", "severity": "high"}, {"type": "missing_env_var", "error": "Missing environment variable: SESSION_SECRET", "severity": "high"}, {"type": "missing_env_var", "error": "Missing environment variable: ADMIN_ACCESS_TOKEN", "severity": "high"}], "runtimeErrors": [], "performanceIssues": [], "securityIssues": [], "dataConsistencyErrors": [], "integrationErrors": [], "summary": {"totalErrors": 4, "criticalErrors": 1, "highErrors": 3, "mediumErrors": 0, "warnings": 0, "status": "critical"}}