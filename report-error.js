#!/usr/bin/env node

// Simple Error Reporting Tool
// Usage: node report-error.js

const readline = require('readline');
const errorLogger = require('./error-logger');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🚨 ERROR REPORTING TOOL');
console.log('========================\n');
console.log('This tool will help you report errors you encounter in the Digital Invoice System.');
console.log('All errors will be saved to log files for analysis.\n');

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer.trim());
    });
  });
}

async function reportError() {
  try {
    console.log('Please provide the following information about the error:\n');

    const errorType = await askQuestion('1. What type of error is this? (api/frontend/database/payment/email/auth/other): ');
    const description = await askQuestion('2. Describe the error in detail: ');
    const stepsToReproduce = await askQuestion('3. What steps did you take that led to this error?: ');
    const expectedBehavior = await askQuestion('4. What did you expect to happen?: ');
    const actualBehavior = await askQuestion('5. What actually happened?: ');
    const additionalInfo = await askQuestion('6. Any additional information (error messages, screenshots description, etc.)?: ');

    // Log the error based on type
    const timestamp = new Date().toISOString();
    const fullDescription = `
ERROR REPORT:
Type: ${errorType}
Description: ${description}
Steps to Reproduce: ${stepsToReproduce}
Expected Behavior: ${expectedBehavior}
Actual Behavior: ${actualBehavior}
Additional Info: ${additionalInfo}
Reported At: ${timestamp}
`;

    switch (errorType.toLowerCase()) {
      case 'api':
        errorLogger.logAPIError('User Reported', 'MANUAL_REPORT', 'ERROR', fullDescription);
        break;
      case 'frontend':
        errorLogger.logFrontendError('User Reported', fullDescription);
        break;
      case 'database':
        errorLogger.logDatabaseError('User Reported', fullDescription);
        break;
      case 'payment':
        errorLogger.logPaymentError('User Reported', 'User Reported', fullDescription);
        break;
      case 'email':
        errorLogger.logEmailError('User Reported', fullDescription);
        break;
      case 'auth':
        errorLogger.logAuthError('User Reported', fullDescription);
        break;
      default:
        errorLogger.logUserError(description, stepsToReproduce, expectedBehavior, actualBehavior);
    }

    console.log('\n✅ Error report saved successfully!');
    console.log('📁 Your error has been logged to: ./error-logs/');
    console.log('🔍 The development team will review this error and provide a fix.');

    const reportAnother = await askQuestion('\nWould you like to report another error? (y/n): ');
    if (reportAnother.toLowerCase() === 'y' || reportAnother.toLowerCase() === 'yes') {
      console.log('\n' + '='.repeat(50) + '\n');
      await reportError();
    } else {
      console.log('\n🎉 Thank you for reporting the error(s)!');
      console.log('📊 You can check the error summary by running: node error-summary.js');
      rl.close();
    }

  } catch (error) {
    console.error('❌ Failed to report error:', error.message);
    rl.close();
  }
}

// Start the error reporting process
reportError();
