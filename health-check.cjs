const axios = require('axios');
const fs = require('fs');

const BASE_URL = 'http://localhost:3001';

console.log('🏥 DIGITAL INVOICE SYSTEM - COMPREHENSIVE HEALTH CHECK');
console.log('======================================================\n');

// Health check results
const results = {
  database: { status: 'unknown', issues: [] },
  api: { status: 'unknown', endpoints: [], issues: [] },
  frontend: { status: 'unknown', issues: [] },
  features: { status: 'unknown', working: [], broken: [] },
  overall: { status: 'unknown', score: 0 }
};

// Test endpoints
const endpoints = [
  { method: 'GET', path: '/', name: 'Homepage', critical: true },
  { method: 'GET', path: '/api/products', name: 'Products API', critical: true },
  { method: 'GET', path: '/api/health', name: 'Health Check', critical: false },
  { method: 'GET', path: '/admin/login', name: 'Admin Login Page', critical: true },
  { method: 'GET', path: '/api/general-settings', name: 'General Settings', critical: false, requiresAuth: true },
  { method: 'GET', path: '/api/smtp-providers', name: 'SMTP Providers', critical: false, requiresAuth: true },
  { method: 'GET', path: '/api/email-templates', name: 'Email Templates', critical: false, requiresAuth: true },
  { method: 'GET', path: '/api/custom-checkout-pages', name: 'Custom Checkout Pages', critical: false, requiresAuth: true },
  { method: 'GET', path: '/api/invoices', name: 'Invoices API', critical: true, requiresAuth: true },
  { method: 'GET', path: '/api/paypal-buttons', name: 'PayPal Buttons', critical: false, requiresAuth: true }
];

async function testEndpoint(endpoint) {
  try {
    const config = {
      method: endpoint.method,
      url: `${BASE_URL}${endpoint.path}`,
      timeout: 5000,
      validateStatus: function (status) {
        // Accept any status code for testing
        return status < 500;
      }
    };

    const response = await axios(config);
    
    const result = {
      name: endpoint.name,
      path: endpoint.path,
      status: response.status,
      success: response.status < 400 || (endpoint.requiresAuth && response.status === 401),
      critical: endpoint.critical,
      responseTime: response.headers['x-response-time'] || 'N/A',
      error: null
    };

    if (endpoint.requiresAuth && response.status === 401) {
      result.note = 'Expected 401 - requires authentication';
      result.success = true;
    }

    return result;
  } catch (error) {
    return {
      name: endpoint.name,
      path: endpoint.path,
      status: error.response?.status || 'ERROR',
      success: false,
      critical: endpoint.critical,
      responseTime: 'N/A',
      error: error.message
    };
  }
}

async function checkDatabase() {
  console.log('📊 DATABASE HEALTH CHECK');
  console.log('========================');
  
  try {
    // Check if database file exists
    const dbExists = fs.existsSync('./data.db');
    console.log(`Database file exists: ${dbExists ? '✅' : '❌'}`);
    
    if (!dbExists) {
      results.database.status = 'critical';
      results.database.issues.push('Database file does not exist');
      return;
    }

    // Check database size
    const stats = fs.statSync('./data.db');
    console.log(`Database size: ${stats.size} bytes`);
    
    if (stats.size === 0) {
      results.database.status = 'critical';
      results.database.issues.push('Database file is empty (0 bytes)');
    } else if (stats.size < 1024) {
      results.database.status = 'warning';
      results.database.issues.push('Database file is very small, may be missing tables');
    } else {
      results.database.status = 'healthy';
    }

  } catch (error) {
    results.database.status = 'critical';
    results.database.issues.push(`Database check failed: ${error.message}`);
  }
}

async function checkAPI() {
  console.log('\n🌐 API ENDPOINTS HEALTH CHECK');
  console.log('=============================');
  
  let successCount = 0;
  let criticalFailures = 0;
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.api.endpoints.push(result);
    
    const statusIcon = result.success ? '✅' : '❌';
    const criticalIcon = result.critical ? '🔴' : '🟡';
    
    console.log(`${statusIcon} ${criticalIcon} ${result.name}`);
    console.log(`   ${result.method} ${result.path} → ${result.status}`);
    
    if (result.note) {
      console.log(`   📝 ${result.note}`);
    }
    
    if (result.error) {
      console.log(`   ⚠️  ${result.error}`);
      results.api.issues.push(`${result.name}: ${result.error}`);
    }
    
    if (result.success) {
      successCount++;
    } else if (result.critical) {
      criticalFailures++;
    }
    
    console.log('');
  }
  
  const successRate = (successCount / endpoints.length) * 100;
  console.log(`📊 API Success Rate: ${successRate.toFixed(1)}% (${successCount}/${endpoints.length})`);
  console.log(`🔴 Critical Failures: ${criticalFailures}`);
  
  if (criticalFailures > 0) {
    results.api.status = 'critical';
  } else if (successRate >= 80) {
    results.api.status = 'healthy';
  } else {
    results.api.status = 'warning';
  }
}

async function checkFrontend() {
  console.log('\n🎨 FRONTEND HEALTH CHECK');
  console.log('========================');
  
  try {
    const response = await axios.get(BASE_URL, { timeout: 10000 });
    
    if (response.status === 200) {
      console.log('✅ Frontend loads successfully');
      
      // Check if it's the React app (look for common React indicators)
      const html = response.data;
      const hasReactRoot = html.includes('id="root"') || html.includes('id="app"');
      const hasViteScript = html.includes('vite') || html.includes('@vite');
      
      if (hasReactRoot) {
        console.log('✅ React root element found');
        results.frontend.status = 'healthy';
      } else {
        console.log('⚠️  React root element not found');
        results.frontend.status = 'warning';
        results.frontend.issues.push('React root element not detected');
      }
      
      if (hasViteScript) {
        console.log('✅ Vite development server detected');
      }
      
    } else {
      console.log(`❌ Frontend returned status: ${response.status}`);
      results.frontend.status = 'critical';
      results.frontend.issues.push(`HTTP ${response.status} response`);
    }
    
  } catch (error) {
    console.log(`❌ Frontend check failed: ${error.message}`);
    results.frontend.status = 'critical';
    results.frontend.issues.push(`Frontend unreachable: ${error.message}`);
  }
}

function generateOverallScore() {
  let score = 0;
  let maxScore = 0;
  
  // Database (30 points)
  maxScore += 30;
  if (results.database.status === 'healthy') score += 30;
  else if (results.database.status === 'warning') score += 15;
  
  // API (40 points)
  maxScore += 40;
  if (results.api.status === 'healthy') score += 40;
  else if (results.api.status === 'warning') score += 20;
  
  // Frontend (30 points)
  maxScore += 30;
  if (results.frontend.status === 'healthy') score += 30;
  else if (results.frontend.status === 'warning') score += 15;
  
  results.overall.score = Math.round((score / maxScore) * 100);
  
  if (results.overall.score >= 80) {
    results.overall.status = 'healthy';
  } else if (results.overall.score >= 60) {
    results.overall.status = 'warning';
  } else {
    results.overall.status = 'critical';
  }
}

function printSummary() {
  console.log('\n📋 HEALTH CHECK SUMMARY');
  console.log('=======================');
  
  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return '✅';
      case 'warning': return '⚠️ ';
      case 'critical': return '🚨';
      default: return '❓';
    }
  };
  
  console.log(`${getStatusIcon(results.database.status)} Database: ${results.database.status.toUpperCase()}`);
  console.log(`${getStatusIcon(results.api.status)} API: ${results.api.status.toUpperCase()}`);
  console.log(`${getStatusIcon(results.frontend.status)} Frontend: ${results.frontend.status.toUpperCase()}`);
  console.log(`${getStatusIcon(results.overall.status)} Overall: ${results.overall.status.toUpperCase()} (${results.overall.score}%)`);
  
  // Print all issues
  const allIssues = [
    ...results.database.issues,
    ...results.api.issues,
    ...results.frontend.issues
  ];
  
  if (allIssues.length > 0) {
    console.log('\n🚨 ISSUES FOUND:');
    allIssues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }
  
  console.log('\n💡 RECOMMENDATIONS:');
  if (results.database.status === 'critical') {
    console.log('- Run database migrations: npm run db:push');
    console.log('- Check DATABASE_URL in .env file');
  }
  if (results.api.status !== 'healthy') {
    console.log('- Check server logs for API errors');
    console.log('- Verify all required environment variables');
  }
  if (results.frontend.status !== 'healthy') {
    console.log('- Check if Vite dev server is running');
    console.log('- Verify React app is building correctly');
  }
}

// Run the health check
async function runHealthCheck() {
  try {
    await checkDatabase();
    await checkAPI();
    await checkFrontend();
    generateOverallScore();
    printSummary();
    
    // Save results to file
    fs.writeFileSync('health-check-results.json', JSON.stringify(results, null, 2));
    console.log('\n📄 Detailed results saved to: health-check-results.json');
    
  } catch (error) {
    console.error('🚨 Health check failed:', error);
    process.exit(1);
  }
}

// Run the health check
runHealthCheck();
