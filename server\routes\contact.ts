import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { sendEmail, sendCustomEmail } from '../services/email';
import { getPaymentConfig } from '../config-storage';
import { createPayPalInvoice } from '../services/paypal-invoice';

export const contactRouter = Router();

// Contact form schema
const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  checkoutPageSlug: z.string().optional(),
});

// Submit contact form
contactRouter.post('/', async (req: Request, res: Response) => {
  try {
    const validatedData = contactFormSchema.parse(req.body);

    // Create a contact inquiry record
    const contactInquiry = {
      id: Date.now(), // Simple ID generation
      name: validatedData.name,
      email: validatedData.email,
      subject: validatedData.subject,
      message: validatedData.message,
      checkoutPageSlug: validatedData.checkoutPageSlug,
      status: 'new',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Store the contact inquiry
    if (storage.createContactInquiry) {
      await storage.createContactInquiry(contactInquiry);
    }

    // Get checkout page settings for SMTP configuration
    let checkoutPage = null;
    if (validatedData.checkoutPageSlug) {
      const allPages = await storage.getCustomCheckoutPages();
      checkoutPage = allPages.find(page => page.slug === validatedData.checkoutPageSlug);
    }

    // Send email notification to admin using the checkout page's SMTP settings
    try {
      const emailContent = `
New Contact Inquiry Received

From: ${validatedData.name}
Email: ${validatedData.email}
Subject: ${validatedData.subject}

Message:
${validatedData.message}

${validatedData.checkoutPageSlug ? `Checkout Page: ${validatedData.checkoutPageSlug}` : ''}

---
This inquiry was submitted through the contact form and has been saved to your admin panel.
You can view and manage it at: ${req.protocol}://${req.get('host')}/admin/contact-inquiries
      `.trim();

      // Use checkout page's SMTP settings if available, otherwise use default
      const smtpSettings = checkoutPage?.smtpSettings || null;

      await sendEmail({
        to: checkoutPage?.supportEmail || '<EMAIL>', // Use checkout page support email or default
        subject: `New Contact Inquiry: ${validatedData.subject}`,
        text: emailContent,
        html: emailContent.replace(/\n/g, '<br>'),
      }, smtpSettings);

      console.log('📧 Contact inquiry email sent successfully');
    } catch (emailError) {
      console.error('Failed to send contact inquiry email:', emailError);
      // Don't fail the request if email sending fails
    }

    // Log the contact inquiry for debugging
    console.log('📧 New contact inquiry received:');
    console.log(`From: ${validatedData.name} (${validatedData.email})`);
    console.log(`Subject: ${validatedData.subject}`);
    console.log(`Checkout Page: ${validatedData.checkoutPageSlug || 'N/A'}`);
    console.log(`Message: ${validatedData.message}`);
    console.log('---');

    res.json({
      success: true,
      message: 'Your message has been sent successfully. We will get back to you soon.'
    });

  } catch (error) {
    console.error('Contact form submission error:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to send message. Please try again later.'
    });
  }
});

// Get contact inquiries (admin only)
contactRouter.get('/', async (req: Request, res: Response) => {
  try {
    // Simple admin check
    if (!req.session?.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get all contact inquiries
    const inquiries = storage.getContactInquiries ? await storage.getContactInquiries() : [];

    res.json(inquiries);
  } catch (error) {
    console.error('Error fetching contact inquiries:', error);
    res.status(500).json({ message: 'Failed to fetch contact inquiries' });
  }
});

// Update contact inquiry status (admin only)
contactRouter.put('/:id', async (req: Request, res: Response) => {
  try {
    if (!req.session?.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const id = parseInt(req.params.id);
    const { status, notes } = req.body;

    if (storage.updateContactInquiry) {
      const updatedInquiry = await storage.updateContactInquiry(id, {
        status,
        notes,
        updatedAt: new Date().toISOString()
      });

      if (!updatedInquiry) {
        return res.status(404).json({ message: 'Contact inquiry not found' });
      }

      res.json(updatedInquiry);
    } else {
      res.status(404).json({ message: 'Contact inquiry not found' });
    }
  } catch (error) {
    console.error('Error updating contact inquiry:', error);
    res.status(500).json({ message: 'Failed to update contact inquiry' });
  }
});

// Generate payment email for a contact inquiry (admin only)
contactRouter.post('/:id/generate-payment-email', async (req: Request, res: Response) => {
  try {
    // Simple admin check
    if (!req.session?.isAdmin) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const inquiryId = parseInt(req.params.id);
    const { customerName, customerEmail } = req.body;

    // Get the contact inquiry
    const inquiries = storage.getContactInquiries ? await storage.getContactInquiries() : [];
    const inquiry = inquiries.find((inq: any) => inq.id === inquiryId);

    if (!inquiry) {
      return res.status(404).json({ message: 'Contact inquiry not found' });
    }

    if (!inquiry.checkoutPageSlug) {
      return res.status(400).json({ message: 'No checkout page associated with this inquiry' });
    }

    // Get the original checkout page
    const checkoutPage = await storage.getCustomCheckoutPageBySlug(inquiry.checkoutPageSlug);
    if (!checkoutPage) {
      return res.status(404).json({ message: 'Original checkout page not found' });
    }

    // Get payment configuration
    const paymentConfig = await getPaymentConfig();

    let paymentContent = '';
    let emailSubject = '';

    // Generate payment method based on checkout page configuration
    if (checkoutPage.paymentMethod === 'paypal-invoice') {
      // Generate PayPal invoice
      try {
        const invoiceData = {
          customerName: customerName || inquiry.name,
          customerEmail: customerEmail || inquiry.email,
          amount: checkoutPage.price.toString(),
          productName: checkoutPage.productName,
          productDescription: checkoutPage.productDescription
        };

        const invoiceUrl = await createPayPalInvoice(invoiceData);

        emailSubject = `Payment Required - ${checkoutPage.productName}`;
        paymentContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
            <div style="text-align: center; margin-bottom: 20px;">
              <h1 style="color: #333; margin-bottom: 10px;">Payment Required</h1>
              <p style="color: #666; font-size: 16px;">Complete your purchase for ${checkoutPage.productName}</p>
            </div>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
              <h3 style="color: #333; margin-top: 0;">Order Details:</h3>
              <p><strong>Invoice Number:</strong> INV-${Date.now()}</p>
              <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
              <p><strong>Product:</strong> ${checkoutPage.productName}</p>
              <p><strong>Description:</strong> ${checkoutPage.productDescription}</p>
              <p><strong>Amount:</strong> $${checkoutPage.price}</p>
              <p><strong>Customer:</strong> ${customerName || inquiry.name}</p>
            </div>

            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
              <p style="margin: 0; color: #856404;"><strong>Note:</strong> After order confirmation, you will receive your subscription by email within 8 hours (during working hours), but generally all subscriptions are sent within 3 hours of your order. Please check your spam folder if you cannot find the email.</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${invoiceUrl}" style="background-color: #0070BA; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Pay with PayPal
              </a>
            </div>

            <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
              <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
              <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
            </div>
          </div>
        `;
      } catch (error) {
        console.error('Error creating PayPal invoice:', error);
        return res.status(500).json({ message: 'Failed to generate PayPal invoice' });
      }
    } else if (checkoutPage.paymentMethod === 'paypal-button-embed' || checkoutPage.paymentMethod === 'trial-paypal-button-embed') {
      // Generate PayPal button HTML
      const buttonId = checkoutPage.paypalButtonId || checkoutPage.trialPaypalButtonId;

      emailSubject = `Payment Required - ${checkoutPage.productName}`;
      paymentContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eaeaea; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h1 style="color: #333; margin-bottom: 10px;">Payment Required</h1>
            <p style="color: #666; font-size: 16px;">Complete your purchase for ${checkoutPage.productName}</p>
          </div>

          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
            <h3 style="color: #333; margin-top: 0;">Order Details:</h3>
            <p><strong>Invoice Number:</strong> INV-${Date.now()}</p>
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
            <p><strong>Product:</strong> ${checkoutPage.productName}</p>
            <p><strong>Description:</strong> ${checkoutPage.productDescription}</p>
            <p><strong>Amount:</strong> $${checkoutPage.price}</p>
            <p><strong>Customer:</strong> ${customerName || inquiry.name}</p>
          </div>

          <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
            <p style="margin: 0; color: #856404;"><strong>Note:</strong> After order confirmation, you will receive your subscription by email within 8 hours (during working hours), but generally all subscriptions are sent within 3 hours of your order. Please check your spam folder if you cannot find the email.</p>
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <p style="color: #666; margin-bottom: 15px;">Click the button below to complete your payment:</p>
            <a href="http://localhost:3001/checkout/${checkoutPage.slug}?email=${encodeURIComponent(customerEmail || inquiry.email)}"
               style="background-color: #0070BA; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              Complete Payment
            </a>
          </div>

          <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; padding: 15px; border-radius: 5px; margin-top: 20px; text-align: center;">
            <p style="margin: 0; color: #495057;">If you have any questions, please reply to this email.</p>
            <p style="margin: 5px 0 0 0; color: #495057;"><strong>Thank you,<br>Support Team</strong></p>
          </div>
        </div>
      `;
    } else {
      return res.status(400).json({ message: 'Unsupported payment method for this checkout page' });
    }

    // Add customer email to allowed list temporarily for payment
    const emailToAllow = customerEmail || inquiry.email;
    if (storage.addAllowedEmail) {
      await storage.addAllowedEmail(emailToAllow);
      console.log(`Added ${emailToAllow} to allowed emails for payment`);
    }

    // Send the payment email
    await sendCustomEmail(
      emailToAllow,
      emailSubject,
      paymentContent
    );

    // Update inquiry status to indicate payment email was sent
    if (storage.updateContactInquiry) {
      await storage.updateContactInquiry(inquiryId, {
        status: 'in_progress',
        notes: (inquiry.notes || '') + `\n\nPayment email sent on ${new Date().toISOString()}\nEmail ${emailToAllow} added to allowed list for payment`
      });
    }

    console.log(`Payment email sent to ${customerEmail || inquiry.email} for inquiry ${inquiryId}`);

    res.json({
      success: true,
      message: 'Payment email sent successfully',
      emailSent: true,
      recipientEmail: customerEmail || inquiry.email
    });
  } catch (error) {
    console.error('Error generating payment email:', error);
    res.status(500).json({ message: 'Failed to generate payment email' });
  }
});
