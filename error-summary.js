#!/usr/bin/env node

// Error Summary and Analysis Tool
// Usage: node error-summary.js

const fs = require('fs');
const path = require('path');

class ErrorAnalyzer {
  constructor() {
    this.logDir = './error-logs';
    this.ensureLogDirectory();
  }

  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      console.log('📁 No error logs directory found. No errors have been logged yet.');
      process.exit(0);
    }
  }

  getLogFiles() {
    try {
      return fs.readdirSync(this.logDir).filter(file => file.endsWith('.log'));
    } catch (error) {
      console.error('❌ Failed to read log directory:', error.message);
      return [];
    }
  }

  analyzeLogFile(filename) {
    const filePath = path.join(this.logDir, filename);
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      const stats = fs.statSync(filePath);
      
      // Count different types of entries
      const errorCount = lines.filter(line => line.includes('ERROR:')).length;
      const warningCount = lines.filter(line => line.includes('WARNING:')).length;
      
      // Get recent entries (last 10)
      const recentEntries = lines.slice(-20).filter(line => line.trim());
      
      return {
        filename,
        size: stats.size,
        lastModified: stats.mtime,
        totalLines: lines.length,
        errorCount,
        warningCount,
        recentEntries: recentEntries.slice(-10) // Last 10 entries
      };
    } catch (error) {
      console.error(`❌ Failed to analyze ${filename}:`, error.message);
      return null;
    }
  }

  generateSummary() {
    const logFiles = this.getLogFiles();
    
    if (logFiles.length === 0) {
      console.log('✅ No error logs found! Your system is running without logged errors.');
      return;
    }

    console.log('📊 ERROR LOG SUMMARY');
    console.log('====================\n');

    const summary = {
      timestamp: new Date().toISOString(),
      totalLogFiles: logFiles.length,
      totalErrors: 0,
      totalWarnings: 0,
      fileAnalysis: []
    };

    logFiles.forEach(filename => {
      const analysis = this.analyzeLogFile(filename);
      if (analysis) {
        summary.fileAnalysis.push(analysis);
        summary.totalErrors += analysis.errorCount;
        summary.totalWarnings += analysis.warningCount;
      }
    });

    // Print summary
    console.log(`📁 Total Log Files: ${summary.totalLogFiles}`);
    console.log(`🚨 Total Errors: ${summary.totalErrors}`);
    console.log(`⚠️  Total Warnings: ${summary.totalWarnings}`);
    console.log(`📅 Generated: ${summary.timestamp}\n`);

    // Print file details
    console.log('📋 LOG FILE DETAILS:');
    console.log('===================');

    summary.fileAnalysis.forEach(file => {
      const errorType = file.filename.split('-')[0].toUpperCase();
      const sizeKB = (file.size / 1024).toFixed(2);
      const lastModified = file.lastModified.toLocaleString();
      
      console.log(`\n📄 ${errorType} ERRORS (${file.filename})`);
      console.log(`   Size: ${sizeKB} KB`);
      console.log(`   Last Modified: ${lastModified}`);
      console.log(`   Total Lines: ${file.totalLines}`);
      console.log(`   Errors: ${file.errorCount}`);
      console.log(`   Warnings: ${file.warningCount}`);
      
      if (file.recentEntries.length > 0) {
        console.log(`   Recent Entries:`);
        file.recentEntries.slice(-3).forEach(entry => {
          const truncated = entry.length > 80 ? entry.substring(0, 80) + '...' : entry;
          console.log(`     ${truncated}`);
        });
      }
    });

    // Save summary to file
    const summaryFile = path.join(this.logDir, 'error-summary.json');
    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
    console.log(`\n💾 Detailed summary saved to: ${summaryFile}`);

    // Provide recommendations
    this.provideRecommendations(summary);

    return summary;
  }

  provideRecommendations(summary) {
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('===================');

    if (summary.totalErrors === 0 && summary.totalWarnings === 0) {
      console.log('🎉 No errors or warnings found! Your system is running smoothly.');
      return;
    }

    if (summary.totalErrors > 0) {
      console.log(`🚨 ${summary.totalErrors} errors found that need attention:`);
      
      summary.fileAnalysis.forEach(file => {
        if (file.errorCount > 0) {
          const errorType = file.filename.split('-')[0];
          console.log(`   - Check ${errorType} errors in ${file.filename}`);
        }
      });
    }

    if (summary.totalWarnings > 0) {
      console.log(`⚠️  ${summary.totalWarnings} warnings found that should be reviewed.`);
    }

    console.log('\n🔧 NEXT STEPS:');
    console.log('   1. Review the error logs above');
    console.log('   2. Share the error-logs folder with the development team');
    console.log('   3. Use "node report-error.js" to report new errors');
    console.log('   4. Run "node error-summary.js" to check for new errors');
  }

  viewSpecificLog(logType) {
    const logFiles = this.getLogFiles();
    const targetFile = logFiles.find(file => file.startsWith(logType));
    
    if (!targetFile) {
      console.log(`❌ No ${logType} log file found.`);
      return;
    }

    const filePath = path.join(this.logDir, targetFile);
    const content = fs.readFileSync(filePath, 'utf8');
    
    console.log(`📄 ${logType.toUpperCase()} ERROR LOG:`);
    console.log('='.repeat(50));
    console.log(content);
  }

  clearLogs() {
    const logFiles = this.getLogFiles();
    
    if (logFiles.length === 0) {
      console.log('✅ No log files to clear.');
      return;
    }

    logFiles.forEach(file => {
      const filePath = path.join(this.logDir, file);
      fs.unlinkSync(filePath);
    });

    console.log(`🗑️  Cleared ${logFiles.length} log files.`);
  }
}

// Command line interface
const args = process.argv.slice(2);
const analyzer = new ErrorAnalyzer();

if (args.length === 0) {
  // Default: show summary
  analyzer.generateSummary();
} else {
  const command = args[0].toLowerCase();
  
  switch (command) {
    case 'summary':
      analyzer.generateSummary();
      break;
    case 'view':
      if (args[1]) {
        analyzer.viewSpecificLog(args[1]);
      } else {
        console.log('❌ Please specify log type: node error-summary.js view [api|database|frontend|etc]');
      }
      break;
    case 'clear':
      analyzer.clearLogs();
      break;
    case 'help':
      console.log('📖 ERROR SUMMARY TOOL USAGE:');
      console.log('=============================');
      console.log('node error-summary.js           - Show error summary');
      console.log('node error-summary.js summary   - Show error summary');
      console.log('node error-summary.js view api  - View specific log type');
      console.log('node error-summary.js clear     - Clear all log files');
      console.log('node error-summary.js help      - Show this help');
      break;
    default:
      console.log('❌ Unknown command. Use "node error-summary.js help" for usage information.');
  }
}
