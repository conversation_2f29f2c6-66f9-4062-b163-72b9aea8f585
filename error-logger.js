// Comprehensive Error Logger for Digital Invoice System
// This script captures all types of errors and saves them to log files

const fs = require('fs');
const path = require('path');

class ErrorLogger {
  constructor() {
    this.logDir = './error-logs';
    this.ensureLogDirectory();
    this.setupGlobalErrorHandlers();
    this.logSessionStart();
  }

  ensureLogDirectory() {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
      console.log('📁 Created error-logs directory');
    }
  }

  getCurrentTimestamp() {
    return new Date().toISOString();
  }

  getLogFileName(type = 'general') {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logDir, `${type}-errors-${date}.log`);
  }

  writeToLog(filename, content) {
    try {
      const timestamp = this.getCurrentTimestamp();
      const logEntry = `[${timestamp}] ${content}\n`;
      fs.appendFileSync(filename, logEntry);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  logSessionStart() {
    const sessionLog = this.getLogFileName('session');
    const separator = '='.repeat(80);
    this.writeToLog(sessionLog, `${separator}`);
    this.writeToLog(sessionLog, `NEW ERROR LOGGING SESSION STARTED`);
    this.writeToLog(sessionLog, `System: Digital Invoice System`);
    this.writeToLog(sessionLog, `Node Version: ${process.version}`);
    this.writeToLog(sessionLog, `Platform: ${process.platform}`);
    this.writeToLog(sessionLog, `${separator}`);
    console.log('🚀 Error logging session started');
  }

  // Log JavaScript errors
  logJavaScriptError(error, context = 'Unknown') {
    const errorLog = this.getLogFileName('javascript');
    const errorInfo = {
      type: 'JAVASCRIPT_ERROR',
      context: context,
      message: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: this.getCurrentTimestamp()
    };
    
    this.writeToLog(errorLog, `JAVASCRIPT ERROR in ${context}:`);
    this.writeToLog(errorLog, `  Message: ${error.message}`);
    this.writeToLog(errorLog, `  Name: ${error.name}`);
    this.writeToLog(errorLog, `  Stack: ${error.stack}`);
    this.writeToLog(errorLog, '---');
    
    console.error('🚨 JavaScript Error logged:', error.message);
  }

  // Log API errors
  logAPIError(endpoint, method, status, error, requestData = null) {
    const apiLog = this.getLogFileName('api');
    this.writeToLog(apiLog, `API ERROR:`);
    this.writeToLog(apiLog, `  Endpoint: ${method} ${endpoint}`);
    this.writeToLog(apiLog, `  Status: ${status}`);
    this.writeToLog(apiLog, `  Error: ${error}`);
    if (requestData) {
      this.writeToLog(apiLog, `  Request Data: ${JSON.stringify(requestData)}`);
    }
    this.writeToLog(apiLog, '---');
    
    console.error('🌐 API Error logged:', `${method} ${endpoint} - ${status}`);
  }

  // Log database errors
  logDatabaseError(operation, error, query = null) {
    const dbLog = this.getLogFileName('database');
    this.writeToLog(dbLog, `DATABASE ERROR:`);
    this.writeToLog(dbLog, `  Operation: ${operation}`);
    this.writeToLog(dbLog, `  Error: ${error}`);
    if (query) {
      this.writeToLog(dbLog, `  Query: ${query}`);
    }
    this.writeToLog(dbLog, '---');
    
    console.error('🗄️ Database Error logged:', operation);
  }

  // Log frontend errors
  logFrontendError(component, error, props = null) {
    const frontendLog = this.getLogFileName('frontend');
    this.writeToLog(frontendLog, `FRONTEND ERROR:`);
    this.writeToLog(frontendLog, `  Component: ${component}`);
    this.writeToLog(frontendLog, `  Error: ${error}`);
    if (props) {
      this.writeToLog(frontendLog, `  Props: ${JSON.stringify(props)}`);
    }
    this.writeToLog(frontendLog, '---');
    
    console.error('🎨 Frontend Error logged:', component);
  }

  // Log authentication errors
  logAuthError(action, error, user = null) {
    const authLog = this.getLogFileName('authentication');
    this.writeToLog(authLog, `AUTHENTICATION ERROR:`);
    this.writeToLog(authLog, `  Action: ${action}`);
    this.writeToLog(authLog, `  Error: ${error}`);
    if (user) {
      this.writeToLog(authLog, `  User: ${user}`);
    }
    this.writeToLog(authLog, '---');
    
    console.error('🔐 Authentication Error logged:', action);
  }

  // Log payment errors
  logPaymentError(provider, operation, error, amount = null) {
    const paymentLog = this.getLogFileName('payment');
    this.writeToLog(paymentLog, `PAYMENT ERROR:`);
    this.writeToLog(paymentLog, `  Provider: ${provider}`);
    this.writeToLog(paymentLog, `  Operation: ${operation}`);
    this.writeToLog(paymentLog, `  Error: ${error}`);
    if (amount) {
      this.writeToLog(paymentLog, `  Amount: ${amount}`);
    }
    this.writeToLog(paymentLog, '---');
    
    console.error('💳 Payment Error logged:', `${provider} - ${operation}`);
  }

  // Log email errors
  logEmailError(operation, error, recipient = null) {
    const emailLog = this.getLogFileName('email');
    this.writeToLog(emailLog, `EMAIL ERROR:`);
    this.writeToLog(emailLog, `  Operation: ${operation}`);
    this.writeToLog(emailLog, `  Error: ${error}`);
    if (recipient) {
      this.writeToLog(emailLog, `  Recipient: ${recipient}`);
    }
    this.writeToLog(emailLog, '---');
    
    console.error('📧 Email Error logged:', operation);
  }

  // Log file system errors
  logFileSystemError(operation, path, error) {
    const fsLog = this.getLogFileName('filesystem');
    this.writeToLog(fsLog, `FILE SYSTEM ERROR:`);
    this.writeToLog(fsLog, `  Operation: ${operation}`);
    this.writeToLog(fsLog, `  Path: ${path}`);
    this.writeToLog(fsLog, `  Error: ${error}`);
    this.writeToLog(fsLog, '---');
    
    console.error('📁 File System Error logged:', operation);
  }

  // Log custom user-reported errors
  logUserError(description, steps, expectedBehavior, actualBehavior) {
    const userLog = this.getLogFileName('user-reported');
    this.writeToLog(userLog, `USER REPORTED ERROR:`);
    this.writeToLog(userLog, `  Description: ${description}`);
    this.writeToLog(userLog, `  Steps to Reproduce: ${steps}`);
    this.writeToLog(userLog, `  Expected Behavior: ${expectedBehavior}`);
    this.writeToLog(userLog, `  Actual Behavior: ${actualBehavior}`);
    this.writeToLog(userLog, '---');
    
    console.error('👤 User Error logged:', description);
  }

  // Setup global error handlers
  setupGlobalErrorHandlers() {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.logJavaScriptError(error, 'Uncaught Exception');
      console.error('🚨 Uncaught Exception:', error);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      const error = new Error(`Unhandled Promise Rejection: ${reason}`);
      this.logJavaScriptError(error, 'Unhandled Promise Rejection');
      console.error('🚨 Unhandled Promise Rejection:', reason);
    });

    // Handle warnings
    process.on('warning', (warning) => {
      const warningLog = this.getLogFileName('warnings');
      this.writeToLog(warningLog, `WARNING:`);
      this.writeToLog(warningLog, `  Name: ${warning.name}`);
      this.writeToLog(warningLog, `  Message: ${warning.message}`);
      this.writeToLog(warningLog, `  Stack: ${warning.stack}`);
      this.writeToLog(warningLog, '---');
    });
  }

  // Generate error summary report
  generateErrorSummary() {
    const summaryFile = path.join(this.logDir, 'error-summary.json');
    const logFiles = fs.readdirSync(this.logDir).filter(file => file.endsWith('.log'));
    
    const summary = {
      timestamp: this.getCurrentTimestamp(),
      totalLogFiles: logFiles.length,
      logFiles: [],
      errorCounts: {}
    };

    logFiles.forEach(file => {
      const filePath = path.join(this.logDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n').filter(line => line.trim());
      
      const fileInfo = {
        filename: file,
        size: fs.statSync(filePath).size,
        lines: lines.length,
        lastModified: fs.statSync(filePath).mtime
      };

      summary.logFiles.push(fileInfo);

      // Count error types
      const errorType = file.split('-')[0];
      summary.errorCounts[errorType] = lines.length;
    });

    fs.writeFileSync(summaryFile, JSON.stringify(summary, null, 2));
    console.log('📊 Error summary generated:', summaryFile);
    
    return summary;
  }

  // Get all error logs for review
  getAllErrorLogs() {
    const logFiles = fs.readdirSync(this.logDir).filter(file => file.endsWith('.log'));
    const allLogs = {};

    logFiles.forEach(file => {
      const filePath = path.join(this.logDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      allLogs[file] = content;
    });

    return allLogs;
  }

  // Clear old logs (optional)
  clearOldLogs(daysOld = 7) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const logFiles = fs.readdirSync(this.logDir);
    let deletedCount = 0;

    logFiles.forEach(file => {
      const filePath = path.join(this.logDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime < cutoffDate) {
        fs.unlinkSync(filePath);
        deletedCount++;
      }
    });

    console.log(`🗑️ Deleted ${deletedCount} old log files`);
    return deletedCount;
  }
}

// Create global error logger instance
const errorLogger = new ErrorLogger();

// Export for use in other modules (both CommonJS and ES modules)
module.exports = errorLogger;
export default errorLogger;

// Also make it available globally
global.errorLogger = errorLogger;

console.log('✅ Error logging system initialized');
console.log('📁 Error logs will be saved to: ./error-logs/');
console.log('📝 Available logging methods:');
console.log('   - errorLogger.logJavaScriptError(error, context)');
console.log('   - errorLogger.logAPIError(endpoint, method, status, error)');
console.log('   - errorLogger.logDatabaseError(operation, error)');
console.log('   - errorLogger.logFrontendError(component, error)');
console.log('   - errorLogger.logAuthError(action, error)');
console.log('   - errorLogger.logPaymentError(provider, operation, error)');
console.log('   - errorLogger.logEmailError(operation, error)');
console.log('   - errorLogger.logUserError(description, steps, expected, actual)');
