const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

console.log('🔍 DIGITAL INVOICE SYSTEM HEALTH CHECK');
console.log('=====================================\n');

// Check multiple possible database locations
const possibleDbPaths = [
  path.join(__dirname, 'data.db'),
  path.join(__dirname, 'server', 'data.db'),
  path.join(__dirname, 'server', 'data', 'database.sqlite')
];

let db = null;
let dbPath = null;

// Find the correct database
for (const testPath of possibleDbPaths) {
  if (fs.existsSync(testPath)) {
    console.log(`✅ Found database at: ${testPath}`);
    dbPath = testPath;
    break;
  } else {
    console.log(`❌ No database at: ${testPath}`);
  }
}

if (!dbPath) {
  console.error('🚨 CRITICAL: No database file found!');
  process.exit(1);
}

try {
  db = new Database(dbPath);
  console.log(`\n📊 DATABASE ANALYSIS`);
  console.log('====================');

  // Get all tables
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  console.log(`\n📋 Found ${tables.length} tables:`);
  tables.forEach(table => console.log(`  - ${table.name}`));

  // Expected tables based on schema
  const expectedTables = [
    'users',
    'products',
    'invoices',
    'custom_checkout_pages',
    'allowed_emails',
    'smtp_providers',
    'email_templates',
    'paypal_buttons',
    'custom_invoices'
  ];

  console.log(`\n🔍 TABLE HEALTH CHECK`);
  console.log('=====================');

  const existingTableNames = tables.map(t => t.name);
  const missingTables = [];

  expectedTables.forEach(expectedTable => {
    if (existingTableNames.includes(expectedTable)) {
      console.log(`✅ ${expectedTable} - EXISTS`);

      // Check table structure and data
      try {
        const count = db.prepare(`SELECT COUNT(*) as count FROM ${expectedTable}`).get();
        console.log(`   📊 Records: ${count.count}`);

        // Get table info
        const tableInfo = db.prepare(`PRAGMA table_info(${expectedTable})`).all();
        console.log(`   🏗️  Columns: ${tableInfo.length}`);

      } catch (error) {
        console.log(`   ⚠️  Error reading table: ${error.message}`);
      }
    } else {
      console.log(`❌ ${expectedTable} - MISSING`);
      missingTables.push(expectedTable);
    }
  });

  // Check for extra tables
  const extraTables = existingTableNames.filter(name =>
    !expectedTables.includes(name) && !name.startsWith('sqlite_')
  );

  if (extraTables.length > 0) {
    console.log(`\n📋 EXTRA TABLES FOUND:`);
    extraTables.forEach(table => console.log(`  + ${table}`));
  }

  console.log(`\n📈 SUMMARY`);
  console.log('==========');
  console.log(`✅ Tables present: ${expectedTables.length - missingTables.length}/${expectedTables.length}`);
  console.log(`❌ Tables missing: ${missingTables.length}`);
  console.log(`📊 Extra tables: ${extraTables.length}`);

  if (missingTables.length > 0) {
    console.log(`\n🚨 MISSING TABLES:`);
    missingTables.forEach(table => console.log(`  - ${table}`));
    console.log(`\n💡 Run database migrations to create missing tables.`);
  }

  db.close();

  console.log(`\n✅ Health check completed successfully!`);

} catch (error) {
  console.error('🚨 CRITICAL ERROR:', error);
  if (db) db.close();
  process.exit(1);
}
