const Database = require('better-sqlite3');
const path = require('path');

// Initialize SQLite database
const dbPath = path.join(__dirname, 'server', 'data', 'database.sqlite');
console.log('Database path:', dbPath);

try {
  const db = new Database(dbPath);
  
  console.log('Adding embed_code_id column to custom_checkout_pages...');
  
  // Check if column already exists
  const tableInfo = db.prepare("PRAGMA table_info(custom_checkout_pages)").all();
  const columnExists = tableInfo.some(col => col.name === 'embed_code_id');
  
  if (columnExists) {
    console.log('Column embed_code_id already exists');
  } else {
    // Add embed_code_id column
    db.exec(`ALTER TABLE custom_checkout_pages ADD COLUMN embed_code_id TEXT`);
    console.log('Successfully added embed_code_id column to custom_checkout_pages');
  }
  
  db.close();
  console.log('Migration completed successfully');
} catch (error) {
  console.error('Migration failed:', error);
  process.exit(1);
}
