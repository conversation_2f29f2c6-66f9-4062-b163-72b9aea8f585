<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Reporter - Digital Invoice System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 1rem;
        }

        select, input, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }

        select:focus, input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            display: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
            display: none;
        }

        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }

        .instructions h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .instructions ul {
            margin-left: 20px;
            color: #424242;
        }

        .instructions li {
            margin-bottom: 5px;
        }

        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 Error Reporter</h1>
            <p>Digital Invoice System - Help us fix issues quickly</p>
        </div>

        <div class="form-container">
            <div class="instructions">
                <h3>📋 How to Report an Error</h3>
                <ul>
                    <li>Fill out all the fields below with as much detail as possible</li>
                    <li>Include exact error messages if you see any</li>
                    <li>Describe the steps you took that led to the error</li>
                    <li>Your report will be saved to error logs for analysis</li>
                </ul>
            </div>

            <div class="success-message" id="successMessage">
                ✅ Error report submitted successfully! Thank you for helping us improve the system.
            </div>

            <div class="error-message" id="errorMessage">
                ❌ Failed to submit error report. Please try again or contact support.
            </div>

            <form id="errorForm">
                <div class="form-group">
                    <label for="errorType">Error Type</label>
                    <select id="errorType" name="errorType" required>
                        <option value="">Select error type...</option>
                        <option value="api">API/Backend Error</option>
                        <option value="frontend">Frontend/UI Error</option>
                        <option value="database">Database Error</option>
                        <option value="payment">Payment Processing Error</option>
                        <option value="email">Email System Error</option>
                        <option value="auth">Authentication/Login Error</option>
                        <option value="performance">Performance Issue</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="description">Error Description</label>
                    <textarea id="description" name="description" placeholder="Describe the error in detail..." required></textarea>
                </div>

                <div class="form-group">
                    <label for="stepsToReproduce">Steps to Reproduce</label>
                    <textarea id="stepsToReproduce" name="stepsToReproduce" placeholder="1. I clicked on...&#10;2. Then I tried to...&#10;3. The error occurred when..." required></textarea>
                </div>

                <div class="form-group">
                    <label for="expectedBehavior">Expected Behavior</label>
                    <textarea id="expectedBehavior" name="expectedBehavior" placeholder="What did you expect to happen?" required></textarea>
                </div>

                <div class="form-group">
                    <label for="actualBehavior">Actual Behavior</label>
                    <textarea id="actualBehavior" name="actualBehavior" placeholder="What actually happened?" required></textarea>
                </div>

                <div class="form-group">
                    <label for="errorMessageText">Error Message (if any)</label>
                    <textarea id="errorMessageText" name="errorMessageText" placeholder="Copy and paste any error messages you saw..."></textarea>
                </div>

                <div class="form-group">
                    <label for="additionalInfo">Additional Information</label>
                    <textarea id="additionalInfo" name="additionalInfo" placeholder="Browser, device, screenshots description, etc."></textarea>
                </div>

                <button type="submit" class="btn">📤 Submit Error Report</button>
            </form>
        </div>

        <div class="footer">
            <p>🔧 This tool helps the development team identify and fix issues quickly</p>
            <p>All reports are saved locally and reviewed for improvements</p>
        </div>
    </div>

    <script>
        document.getElementById('errorForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const errorData = Object.fromEntries(formData.entries());
            
            // Add timestamp
            errorData.timestamp = new Date().toISOString();
            errorData.userAgent = navigator.userAgent;
            errorData.url = window.location.href;
            
            try {
                // Save to localStorage as backup
                const existingErrors = JSON.parse(localStorage.getItem('errorReports') || '[]');
                existingErrors.push(errorData);
                localStorage.setItem('errorReports', JSON.stringify(existingErrors));
                
                // Try to send to server (if endpoint exists)
                try {
                    await fetch('/api/error-report', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(errorData)
                    });
                } catch (serverError) {
                    console.log('Server endpoint not available, saved locally');
                }
                
                // Show success message
                document.getElementById('successMessage').style.display = 'block';
                document.getElementById('errorMessage').style.display = 'none';
                
                // Reset form
                this.reset();
                
                // Scroll to top
                window.scrollTo(0, 0);
                
                // Hide success message after 5 seconds
                setTimeout(() => {
                    document.getElementById('successMessage').style.display = 'none';
                }, 5000);
                
            } catch (error) {
                console.error('Failed to submit error report:', error);
                document.getElementById('errorMessage').style.display = 'block';
                document.getElementById('successMessage').style.display = 'none';
                window.scrollTo(0, 0);
            }
        });

        // Auto-save form data as user types
        const formInputs = document.querySelectorAll('input, textarea, select');
        formInputs.forEach(input => {
            input.addEventListener('input', function() {
                const formData = new FormData(document.getElementById('errorForm'));
                const data = Object.fromEntries(formData.entries());
                localStorage.setItem('errorFormDraft', JSON.stringify(data));
            });
        });

        // Restore form data on page load
        window.addEventListener('load', function() {
            const savedData = localStorage.getItem('errorFormDraft');
            if (savedData) {
                const data = JSON.parse(savedData);
                Object.keys(data).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = data[key];
                    }
                });
            }
        });
    </script>
</body>
</html>
