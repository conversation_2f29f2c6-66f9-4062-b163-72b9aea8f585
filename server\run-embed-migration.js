import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize SQLite database
const dbPath = path.join(__dirname, 'data', 'database.sqlite');
const sqlite = new Database(dbPath);

async function addEmbedCodeToCheckout() {
  console.log('Adding embed_code_id column to custom_checkout_pages...');

  try {
    // Add embed_code_id column
    sqlite.exec(`
      ALTER TABLE custom_checkout_pages
      ADD COLUMN embed_code_id TEXT
    `);

    console.log('Successfully added embed_code_id column to custom_checkout_pages');
  } catch (error) {
    console.error('Error adding embed_code_id column to custom_checkout_pages:', error);
    throw error;
  }
}

addEmbedCodeToCheckout()
  .then(() => {
    console.log('Migration completed successfully');
    sqlite.close();
    process.exit(0);
  })
  .catch((error) => {
    console.error('Migration failed:', error);
    sqlite.close();
    process.exit(1);
  });
