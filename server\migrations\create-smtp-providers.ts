import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import { sql } from 'drizzle-orm';
import { configStorage } from '../config-storage';

console.log('Creating SMTP providers table and migrating current configuration...');

// Initialize database
const sqlite = new Database('data.db');
const db = drizzle(sqlite);

async function createSmtpProvidersTable() {
  console.log('Creating smtp_providers table...');
  
  try {
    // Create the smtp_providers table
    await db.run(sql`
      CREATE TABLE IF NOT EXISTS smtp_providers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        host TEXT NOT NULL,
        port TEXT NOT NULL,
        secure INTEGER NOT NULL DEFAULT 0,
        username TEXT NOT NULL,
        password TEXT NOT NULL,
        from_email TEXT NOT NULL,
        from_name TEXT NOT NULL,
        active INTEGER NOT NULL DEFAULT 1,
        is_default INTEGER NOT NULL DEFAULT 0,
        is_backup INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `);
    
    console.log('✅ smtp_providers table created successfully');
    
    // Check if we already have data in the table
    const existingProviders = await db.run(sql`SELECT COUNT(*) as count FROM smtp_providers`);
    
    if (existingProviders.count === 0) {
      console.log('Migrating current SMTP configuration from config-storage...');
      
      // Get current SMTP providers from config storage
      const currentProviders = configStorage.email.providers;
      
      for (const provider of currentProviders) {
        const now = new Date().toISOString();
        
        await db.run(sql`
          INSERT INTO smtp_providers (
            id, name, host, port, secure, username, password, 
            from_email, from_name, active, is_default, is_backup,
            created_at, updated_at
          ) VALUES (
            ${provider.id},
            ${provider.name},
            ${provider.credentials.host},
            ${provider.credentials.port},
            ${provider.credentials.secure ? 1 : 0},
            ${provider.credentials.auth.user},
            ${provider.credentials.auth.pass},
            ${provider.credentials.fromEmail},
            ${provider.credentials.fromName},
            ${provider.active ? 1 : 0},
            ${provider.isDefault ? 1 : 0},
            ${provider.isBackup ? 1 : 0},
            ${now},
            ${now}
          )
        `);
        
        console.log(`✅ Migrated SMTP provider: ${provider.name}`);
      }
      
      console.log('✅ SMTP configuration migration completed');
    } else {
      console.log('SMTP providers table already contains data, skipping migration');
    }
    
  } catch (error) {
    console.error('Error creating smtp_providers table:', error);
    throw error;
  }
}

// Run the migration
createSmtpProvidersTable()
  .then(() => {
    console.log('✅ SMTP providers migration completed successfully');
    sqlite.close();
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ SMTP providers migration failed:', error);
    sqlite.close();
    process.exit(1);
  });
