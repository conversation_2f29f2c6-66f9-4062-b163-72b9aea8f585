/**
 * Email domain validator utility
 * Validates if an email domain is from an allowed list of providers
 */

// List of allowed email domains
const ALLOWED_DOMAINS = [
  // Google
  'gmail.com',
  'googlemail.com',

  // Microsoft
  'hotmail.com',
  'outlook.com',
  'live.com',
  'msn.com',

  // Yahoo
  'yahoo.com',
  'yahoo.co.uk',
  'yahoo.co.jp',
  'yahoo.fr',
  'yahoo.de',
  'yahoo.it',
  'yahoo.es',
  'yahoo.ca',
  'ymail.com',

  // Other common providers
  'aol.com',
  'icloud.com',
  'me.com',
  'mac.com',
  'mail.com',
  'protonmail.com',
  'proton.me',
  'zoho.com',
  'gmx.com',
  'gmx.net',
  'gmx.de',
];

/**
 * Checks if an email domain is allowed
 * @param email The email address to validate
 * @returns An object with validation result and message
 */
export function validateEmailDomain(email: string): { isValid: boolean; message?: string } {
  if (!email || !email.includes('@')) {
    return { isValid: false, message: 'Please enter a valid email address' };
  }

  // Accept all email domains - no domain restrictions
  // Only individual email blocking through allowed emails database will be enforced
  return { isValid: true };
}

/**
 * Checks if an email domain is allowed and returns a boolean
 * @param email The email address to validate
 * @returns True if the email domain is allowed, false otherwise
 */
export function isAllowedEmailDomain(email: string): boolean {
  return validateEmailDomain(email).isValid;
}
