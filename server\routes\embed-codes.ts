import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { nanoid } from 'nanoid';
import { isAdmin } from '../middleware/auth';
import { storage } from '../storage';
import { EmbedCode, EMBED_CODE_TEMPLATES } from '../../shared/embed-codes';

const embedCodesRouter = Router();

// Schema for creating/updating embed codes
const embedCodeSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  platform: z.string().min(1, 'Platform is required'),
  headScript: z.string().min(1, 'Head script is required'),
  buttonHtml: z.string().min(1, 'Button HTML is required'),
  active: z.boolean().default(true),
});

// Get all embed codes (admin only)
embedCodesRouter.get('/', isAdmin, async (req: Request, res: Response) => {
  try {
    const embedCodes = await storage.getEmbedCodes();
    res.json(embedCodes);
  } catch (error) {
    console.error('Error fetching embed codes:', error);
    res.status(500).json({ message: 'Failed to fetch embed codes' });
  }
});

// Get embed code templates
embedCodesRouter.get('/templates', isAdmin, async (req: Request, res: Response) => {
  try {
    res.json(EMBED_CODE_TEMPLATES);
  } catch (error) {
    console.error('Error fetching embed code templates:', error);
    res.status(500).json({ message: 'Failed to fetch embed code templates' });
  }
});

// Create a new embed code (admin only)
embedCodesRouter.post('/', isAdmin, async (req: Request, res: Response) => {
  try {
    const validatedData = embedCodeSchema.parse(req.body);

    const embedCode: EmbedCode = {
      id: nanoid(),
      ...validatedData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    const createdEmbedCode = await storage.createEmbedCode(embedCode);
    res.status(201).json(createdEmbedCode);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    console.error('Error creating embed code:', error);
    res.status(500).json({ message: 'Failed to create embed code' });
  }
});

// Update an embed code (admin only)
embedCodesRouter.put('/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const validatedData = embedCodeSchema.parse(req.body);

    const updatedEmbedCode = await storage.updateEmbedCode(id, {
      ...validatedData,
      updatedAt: new Date().toISOString(),
    });

    if (!updatedEmbedCode) {
      return res.status(404).json({ message: 'Embed code not found' });
    }

    res.json(updatedEmbedCode);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        message: 'Validation error',
        errors: error.errors
      });
    }
    console.error('Error updating embed code:', error);
    res.status(500).json({ message: 'Failed to update embed code' });
  }
});

// Delete an embed code (admin only)
embedCodesRouter.delete('/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const deleted = await storage.deleteEmbedCode(id);

    if (!deleted) {
      return res.status(404).json({ message: 'Embed code not found' });
    }

    res.json({ message: 'Embed code deleted successfully' });
  } catch (error) {
    console.error('Error deleting embed code:', error);
    res.status(500).json({ message: 'Failed to delete embed code' });
  }
});

// Get a specific embed code by ID (admin only)
embedCodesRouter.get('/:id', isAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const embedCode = await storage.getEmbedCode(id);

    if (!embedCode) {
      return res.status(404).json({ message: 'Embed code not found' });
    }

    res.json(embedCode);
  } catch (error) {
    console.error('Error fetching embed code:', error);
    res.status(500).json({ message: 'Failed to fetch embed code' });
  }
});

export { embedCodesRouter };
