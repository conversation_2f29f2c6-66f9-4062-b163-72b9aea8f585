import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  url: string,
  method: string = 'GET',
  data?: unknown | undefined,
): Promise<any> {
  // Only log in development mode and for non-frequent requests
  if (process.env.NODE_ENV === 'development' && !url.includes('check-session')) {
    console.log(`API Request: ${method} ${url}`, data);
  }

  const options = {
    method,
    headers: data ? {
      "Content-Type": "application/json",
    } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include" as RequestCredentials, // This ensures cookies are sent with the request
  };

  try {
    const res = await fetch(url, options);

    if (!res.ok) {
      let errorText;
      try {
        errorText = await res.text();

        // Try to parse the error as JSON
        try {
          const errorJson = JSON.parse(errorText);
          if (errorJson.message) {
            throw new Error(errorJson.message);
          }
        } catch (jsonError) {
          // Not JSON, continue with text
        }
      } catch (textError) {
        errorText = res.statusText;
      }

      throw new Error(errorText || res.statusText);
    }

    const responseData = await res.json();

    // Only log response data for non-frequent requests in development
    if (process.env.NODE_ENV === 'development' && !url.includes('check-session')) {
      console.log('Response data:', responseData);
    }

    return responseData;
  } catch (error) {
    // Only log errors for important requests
    if (!url.includes('check-session')) {
      console.error('API Request failed:', error);
    }
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    try {
      return await apiRequest(queryKey[0] as string, 'GET');
    } catch (error: any) {
      if (unauthorizedBehavior === "returnNull" && error.message?.includes('401')) {
        return null;
      }
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
